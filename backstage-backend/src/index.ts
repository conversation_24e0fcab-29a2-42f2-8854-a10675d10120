/*
 * Hi!
 *
 * Note that this is an EXAMPLE Backstage backend. Please check the README.
 *
 * Happy hacking!
 */

import { createBackend } from "@backstage/backend-defaults";

import { customOktaAuth } from "./extensions/oktaCustomAuth";

const backend = createBackend();

// Core backend plugins
backend.add(import("@backstage/plugin-proxy-backend"));
backend.add(import("@backstage/plugin-scaffolder-backend"));
backend.add(import("@backstage/plugin-techdocs-backend"));

// Auth plugin with the new modules
backend.add(import("@backstage/plugin-auth-backend"));
backend.add(import("@backstage/plugin-auth-backend-module-guest-provider"));
backend.add(customOktaAuth);

// Catalog plugin with all its modules
backend.add(import("@backstage/plugin-catalog-backend"));
backend.add(import("@backstage/plugin-catalog-backend-module-scaffolder-entity-model"));
backend.add(import("@backstage/plugin-catalog-backend-module-logs"));
backend.add(import("@backstage/plugin-catalog-backend-module-bitbucket-server"));
backend.add(import("@backstage/plugin-catalog-backend-module-ldap"));

// Permission backend
backend.add(import("@backstage/plugin-permission-backend"));
backend.add(import("./extensions/permissionsPolicyExtension"));
backend.add(import("./extensions/catalogPermissionRules"));

// Search backend
backend.add(import("@backstage/plugin-search-backend"));
backend.add(import("@backstage/plugin-search-backend-module-pg"));
backend.add(import("@backstage/plugin-search-backend-module-catalog"));
backend.add(import("@backstage/plugin-search-backend-module-techdocs"));

// Other plugins
backend.add(import("@backstage/plugin-kubernetes-backend"));
backend.add(import("@backstage/plugin-scaffolder-backend-module-bitbucket-server"));
backend.add(import("@backstage-community/plugin-sonarqube-backend"));

backend.start();

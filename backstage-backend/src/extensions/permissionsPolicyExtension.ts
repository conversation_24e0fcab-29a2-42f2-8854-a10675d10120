import { createBackendModule, coreServices } from "@backstage/backend-plugin-api";
import { Config } from "@backstage/config";
import { PolicyDecision, AuthorizeResult } from "@backstage/plugin-permission-common";
import { PermissionPolicy, PolicyQuery, PolicyQueryUser } from "@backstage/plugin-permission-node";
import { policyExtensionPoint } from "@backstage/plugin-permission-node/alpha";

export const isTypeServiceTemplateRule = {
  name: "IS_TYPE_SERVICE_TEMPLATE",
  description: "Check if entity is a Service or Template",
  resourceType: "catalog-entity" as const,
  apply: (resource: any) => resource.spec?.type === "service" || resource.kind === "Template",
  toQuery: () => ({ key: "spec.type", values: ["service"] }),
};

export const isInSystemRule = {
  name: "IS_IN_SYSTEM",
  description: "Check if entity is in the system",
  resourceType: "catalog-entity" as const,
  apply: (resource: any) => !!resource,
  toQuery: () => ({ key: "metadata.name", values: ["*"] }),
};

class CustomPermissionPolicy implements PermissionPolicy {
  private config: Config;
  private permissionServiceToken: string | null = null;
  private backstageUrl: string;

  constructor(config: Config) {
    this.config = config;
    this.backstageUrl = config.getString("backend.baseUrl");
  }

  private async getPermissionServiceToken(): Promise<string> {
    try {
      const username = this.config.getOptionalString("backend.permissionservice.username");
      const password = this.config.getOptionalString("backend.permissionservice.password");

      const response = await fetch(`${this.backstageUrl}/api/proxy/permission-service/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        throw new Error(`Erro ao obter token: status ${response.status}`);
      }

      const data = await response.json();
      if (!data?.token) {
        throw new Error("Token não encontrado na resposta do serviço de permissionamento");
      }

      return data.token;
    } catch (error) {
      throw error;
    }
  }

  private async getPolicyDecision(request: PolicyQuery, user?: PolicyQueryUser): Promise<PolicyDecision> {
    try {
      if (!this.permissionServiceToken) {
        this.permissionServiceToken = await this.getPermissionServiceToken();
      }

      const userInfo = user
        ? {
            identity: {
              ownershipEntityRefs: user.identity?.ownershipEntityRefs || [],
            },
          }
        : undefined;

      const payload = {
        permission: {
          name: request.permission.name,
          resourceType: "catalog-entity",
        },
        user: userInfo,
        resource: request.resource || {},
        token: this.permissionServiceToken,
      };

      console.debug(`[permissionsPolicyExtension] Sending permission request:`, JSON.stringify(payload, null, 2));

      const response = await fetch(`${this.backstageUrl}/api/proxy/permission-service/authorize`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.permissionServiceToken}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`Erro ao autorizar: status ${response.status}`);
      }

      const policyDecision = (await response.json()) as PolicyDecision;
      return policyDecision;
    } catch (error) {
      return { result: AuthorizeResult.DENY };
    }
  }

  async handle(request: PolicyQuery, user?: PolicyQueryUser): Promise<PolicyDecision> {
    try {
      // return { result: AuthorizeResult.ALLOW };
      return await this.getPolicyDecision(request, user);
    } catch (error) {
      if (error instanceof Error && error.message.includes("status 401")) {
        try {
          this.permissionServiceToken = null;
          return await this.getPolicyDecision(request, user);
        } catch (retryError) {
          return { result: AuthorizeResult.DENY };
        }
      }
      return { result: AuthorizeResult.ALLOW };
    }
  }
}

export default createBackendModule({
  pluginId: "permission",
  moduleId: "permission-policy",
  register(reg) {
    reg.registerInit({
      deps: { policy: policyExtensionPoint, config: coreServices.rootConfig },
      async init({ policy, config }) {
        policy.setPolicy(new CustomPermissionPolicy(config));
      },
    });
  },
});

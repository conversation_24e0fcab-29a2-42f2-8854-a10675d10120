import { createBackendModule } from '@backstage/backend-plugin-api';
import { authProvidersExtensionPoint, createOAuthProviderFactory } from '@backstage/plugin-auth-node';

import { oktaAuthenticator } from '@backstage/plugin-auth-backend-module-okta-provider';
import { getDefaultOwnershipEntityRefs } from '@backstage/plugin-auth-backend';
import { stringifyEntityRef } from '@backstage/catalog-model';

export const customOktaAuth = createBackendModule({
    // This ID must be exactly "auth" because that's the plugin it targets
    pluginId: 'auth',
    // This ID must be unique, but can be anything
    moduleId: 'custom-auth-provider',
    register(reg) {
      reg.registerInit({
        deps: { providers: authProvidersExtensionPoint },
        async init({ providers }) {
          providers.registerProvider({
            // This ID must match the actual provider config, e.g. addressing
            // auth.providers.github means that this must be "github".
            providerId: 'okta',
            // Use createProxyAuthProviderFactory instead if it's one of the proxy
            // based providers rather than an OAuth based one
            factory: createOAuthProviderFactory({
              authenticator: oktaAuthenticator,
              async signInResolver(info, ctx) {
  
                const { result } = info;
  
                if (!result.fullProfile.username)
                  throw new Error("Problemas ao buscar o login do usuario");
  
                const username = result.fullProfile.username?.split('@')[0];
  
                if (!username)
                  throw new Error("Problemas ao buscar o login do usuario");
  
  
                const { entity } = await ctx.findCatalogUser({
                  entityRef: {
                    kind: 'User',
                    namespace: 'default',
                    name: username
                  }
                });
  
                if (!entity)
                  throw new Error("Problemas ao buscar o login do usuario");
  
                const ownershipRefs = getDefaultOwnershipEntityRefs(entity);
  
                return ctx.issueToken({
                  claims: {
                    sub: stringifyEntityRef(entity),
                    ent: ownershipRefs,
                  },
                });
  
              },
            }),
          });
        },
      });
    },
  });
FROM node:20-bookworm-slim

WORKDIR /app

# Instalar dependências necessárias
RUN apt-get update && \
    apt-get install -y --no-install-recommends python3 g++ build-essential && \
    rm -rf /var/lib/apt/lists/*

# Copiar arquivos necessários
COPY dist/package.json ./package.json
COPY dist/bundle.tar.gz ./bundle.tar.gz
COPY app-config*.yaml ./

# Extrair o bundle
RUN mkdir -p bundle && \
    tar xzf bundle.tar.gz -C bundle && \
    ls -la bundle && \
    rm bundle.tar.gz

# Instalar dependências de produção
RUN yarn install --production --frozen-lockfile

ENV NODE_ENV=production
ENV NODE_OPTIONS=--no-node-snapshot
ENV HOST=0.0.0.0

EXPOSE 7007

# Executar a aplicação com o caminho correto
CMD ["node", "bundle/dist/index.cjs.js", "--config", "app-config.yaml"] 
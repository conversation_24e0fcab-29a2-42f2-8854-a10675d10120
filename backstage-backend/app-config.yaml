app:
  title: Portal Self Service da Engie
  baseUrl: http://localhost:3000

  support:
    url: https://github.com/backstage/backstage/issues # Used by common ErrorPage
    items: # Used by common SupportButton component
      - title: Documentação DevSecOps
        icon: docs
        links:
          - url: http://almappro.tractebelenergia.com.br/confluence/display/DEV/DevSecOps?src=sidebar
            title: "Documentação Devops"
      - title: Chamado
        icon: github
        links:
          - url: https://jira.engie.com.br/servicedesk/customer/portal/1/create/125
            title: Abrir Chamado

organization:
  name: Engie

backend:
  # Used for enabling authentication, secret is shared by all backend plugins
  # See https://backstage.io/docs/auth/service-to-service-auth for
  # information on the format
  auth:
    dangerouslyDisableDefaultAuthPolicy: true
    keys:
      - secret: ${BACKEND_SECRET}
  baseUrl: http://localhost:7007
  permissionservice:
    username: ${PERMISSION_SERVICE_USERNAME}
    password: ${PERMISSION_SERVICE_PASSWORD}
    # username: admin
    # password: admin
  listen:
    port: 7007
    # Uncomment the following host directive to bind to specific interfaces
    # host: 127.0.0.1
  csp:
    connect-src: ["'self'", "http:", "https:"]
    # Content-Security-Policy directives follow the Helmet format: https://helmetjs.github.io/#reference
    # Default Helmet Content-Security-Policy values can be removed by setting the key to false
  cors:
    origin: http://localhost:3000
    methods: [GET, HEAD, PATCH, POST, PUT, DELETE]
    credentials: true
  # This is for local development only, it is not recommended to use this in production
  # The production database configuration is stored in app-config.production.yaml
  database:
    client: pg
    connection:
      host: ${POSTGRES_HOST}
      port: ${POSTGRES_PORT}
      user: ${POSTGRES_USER}
      password: ${POSTGRES_PASSWORD}
  # workingDirectory: /tmp # Use this to configure a working directory for the scaffolder, defaults to the OS temp-dir

bamboo:
  baseUrl: http://almappro.tractebelenergia.com.br/bamboo

apim:
  baseUrl: https://api-manager.dev.engieenergia.com.br/

integrations:
  bitbucketServer:
    - host: almappro.tractebelenergia.com.br
      apiBaseUrl: http://almappro.tractebelenergia.com.br/bitbucket/rest/api/1.0
      username: ${BITBUCKET_SERVER_USERNAME}
      password: ${BITBUCKET_SERVER_PASSWORD}

kubernetes:
  objectTypes:
    - pods
    - services
    - configmaps
    - limitranges
    - resourcequotas
    - deployments
    - replicasets
    - jobs
    - cronjobs
    - ingresses
  serviceLocatorMethod:
    type: singleTenant
  clusterLocatorMethods:
    - type: config
      clusters:
        - url: https://*************:6443
          name: ocp4
          customResources:
            - group: "route.openshift.io"
              apiVersion: "v1"
              plural: "routes"
            - group: "apps.openshift.io"
              apiVersion: "v1"
              plural: "deploymentconfigs"
          authProvider: serviceAccount
          skipTLSVerify: true
          skipMetricsLookup: false
          serviceAccountToken: ${OCP_SONAR_TOKEN}
        - url: https://3C1695B215DC64F358BAB84B4615B80D.gr7.us-east-1.eks.amazonaws.com
          name: eks-backoffice - DES/HML
          authProvider: serviceAccount
          skipTLSVerify: true
          skipMetricsLookup: false
          serviceAccountToken: ${EKS_BACKOFFICE_DEV}
        - url: https://E6D8FE42B03650E73CD3B696D81660AD.gr7.us-east-1.eks.amazonaws.com
          name: eks-backoffice - PRD
          authProvider: serviceAccount
          skipTLSVerify: true
          skipMetricsLookup: false
          serviceAccountToken: ${EKS_BACKOFFICE_PRD}

proxy:
  endpoints:
    "/bamboo":
      target: "http://almappro.tractebelenergia.com.br/bamboo/rest/api"
      headers:
        Authorization: ${BAMBOO_LOGIN}
        Content-Type: Application/json
        Accept: Application/json
    "/apim":
      target: "https://api-manager.dev.engieenergia.com.br/api/portal/v1.3"
      headers:
        Authorization: Basic ${APIM_LOGIN}
        Content-Type: Application/json
        Accept: Application/json
    "/grafana/api":
      # May be a public or an internal DNS
      target: http://br-ebe-grafana.ds55.local:3000/
      headers:
        Authorization: Bearer ${GRAFANA_TOKEN}
    "/permission-service":
      target: "${backend.permissionservice.url}"
      # target: "http://localhost:3333"
      headers:
        Content-Type: Application/json
        Accept: Application/json

grafana:
  # Publicly accessible domain
  domain: http://br-ebe-grafana.ds55.local:3000

  # Is unified alerting enabled in Grafana?
  # See: https://grafana.com/blog/2021/06/14/the-new-unified-alerting-system-for-grafana-everything-you-need-to-know/
  # Optional. Default: false
  unifiedAlerting: false

# Reference documentation http://backstage.io/docs/features/techdocs/configuration
# Note: After experimenting with basic setup, use CI/CD to generate docs
# and an external cloud storage when deploying TechDocs for production use-case.
# https://backstage.io/docs/features/techdocs/how-to-guides#how-to-migrate-from-techdocs-basic-to-recommended-deployment-approach
techdocs:
  builder: "local" # Alternatives - 'external'
  generator:
    runIn: "docker" # Alternatives - 'local'
  publisher:
    type: "local" # Alternatives - 'googleGcs' or 'awsS3'. Read documentation for using alternatives.

auth:
  environment: development
  providers:
    okta:
      development:
        clientId: ${AUTH_OKTA_CLIENT_ID}
        clientSecret: ${AUTH_OKTA_CLIENT_SECRET}
        audience: ${AUTH_OKTA_URL}
        # signIn:
        #   resolvers:
        #     - resolver: emailMatchingUserEntityProfileEmail
    guest: {}
  autologout:
    enabled: true
sonarqube:
  baseUrl: http://sonar.ds55.local
  apiKey: ${SONAR_TOKEN}

scaffolder:
  defaultAuthor:
    name: Backstage # Defaults to `Scaffolder`
    email: <EMAIL> # Defaults to `<EMAIL>`
  defaultCommitMessage: "Iniciando Projeto" # Defaults to 'Initial commit'

catalog:
  providers:
    ldapOrg:
      default:
        target: ldaps://d92.tes.local:636
        tls:
          rejectUnauthorized: false
          certs: "/cert/d92.crt"
        bind:
          dn: CN=UMRS62,OU=Servicos,OU=Administracao,OU=BR02,DC=D92,DC=tes,DC=local
          secret: ${LDAP_SECRET}
        schedule:
          frequency: PT1H
          timeout: PT15M
        users:
          dn: OU=Usuarios,OU=sede,OU=BR02,DC=D92,DC=tes,DC=local
          options:
            scope: one
            paged: true
            filter: (&(objectCategory=Person)(sAMAccountName=*) (|(mail=*<EMAIL>*)(mail=*<EMAIL>*)) (!(description=*nomeSponsor*)) )
          map:
            name: cn
            description: description
            displayName: cn
            email: mail
            memberOf: memberOf
        groups:
          dn: OU=ALM,OU=Grupos,OU=Administracao,OU=BR02,DC=D92,DC=tes,DC=local
          options:
            scope: one
            paged: true
            filter: (&(objectCategory=Group) (|(description=*ADMINISTRADORES DO ALM*)))
          map:
            name: cn
            members: member
  import:
    entityFilename: catalog-info.yaml
    pullRequestBranchName: backstage-integration
  rules:
    - allow: [Component, System, API, Resource, Location, Template]
  locations:
    ## Uncomment these lines to add more example data
    # - type: url
    #   target: https://github.com/backstage/backstage/blob/master/packages/catalog-model/examples/all.yaml

    ## Uncomment these lines to add an example org
    # - type: url
    #   target: https://github.com/backstage/backstage/blob/master/packages/catalog-model/examples/acme-corp.yaml
    #   rules:
    #     - allow: [User, Group]

# see https://backstage.io/docs/permissions/getting-started for more on the permission framework
permission:
  # setting this to `false` will disable permissions
  enabled: true

version: "3.8"

services:
  postgres:
    image: postgres:13
    container_name: postgres
    env_file:
      - .env
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_DB=backstage
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - backstage-network
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "${POSTGRES_USER}"]
      interval: 5s
      timeout: 5s
      retries: 5

  backstage-backend:
    image: backstage-backend:latest
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backstage-backend
    ports:
      - "7007:7007"
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - backstage-network
    env_file:
      - .env
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=backstage

networks:
  backstage-network:
    driver: bridge

volumes:
  postgres-data:

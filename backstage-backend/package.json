{"name": "backend", "version": "0.0.0", "main": "dist/index.cjs.js", "types": "src/index.ts", "private": true, "backstage": {"role": "backend"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "build-image": "docker build ../.. -f <PERSON><PERSON><PERSON><PERSON> --tag backstage", "commit": "git add . && git-cz && git push"}, "dependencies": {"@backstage-community/plugin-sonarqube-backend": "^0.3.0", "@backstage/backend-defaults": "^0.9.0", "@backstage/backend-plugin-api": "^1.3.0", "@backstage/config": "^1.3.2", "@backstage/plugin-auth-backend": "^0.24.5", "@backstage/plugin-auth-backend-module-github-provider": "^0.3.2", "@backstage/plugin-auth-backend-module-guest-provider": "^0.2.7", "@backstage/plugin-auth-backend-module-okta-provider": "^0.2.2", "@backstage/plugin-auth-node": "^0.6.2", "@backstage/plugin-catalog-backend": "^1.32.1", "@backstage/plugin-catalog-backend-module-bitbucket-server": "^0.4.0", "@backstage/plugin-catalog-backend-module-ldap": "^0.11.4", "@backstage/plugin-catalog-backend-module-logs": "^0.1.9", "@backstage/plugin-catalog-backend-module-scaffolder-entity-model": "^0.2.7", "@backstage/plugin-catalog-node": "^1.16.3", "@backstage/plugin-kubernetes-backend": "^0.19.5", "@backstage/plugin-permission-backend": "^0.6.0", "@backstage/plugin-permission-backend-module-allow-all-policy": "^0.2.7", "@backstage/plugin-permission-common": "^0.8.4", "@backstage/plugin-permission-node": "^0.9.1", "@backstage/plugin-proxy-backend": "^0.6.1", "@backstage/plugin-scaffolder-backend": "^1.32.1", "@backstage/plugin-scaffolder-backend-module-bitbucket-server": "^0.2.8", "@backstage/plugin-search-backend": "^2.0.1", "@backstage/plugin-search-backend-module-catalog": "^0.3.3", "@backstage/plugin-search-backend-module-pg": "^0.5.43", "@backstage/plugin-search-backend-module-techdocs": "^0.4.1", "@backstage/plugin-search-backend-node": "^1.3.10", "@backstage/plugin-techdocs-backend": "^2.0.1", "better-sqlite3": "^9.0.0", "git-cz": "^4.9.0", "node-gyp": "^10.0.0", "pg": "^8.11.3"}, "devDependencies": {"@backstage/cli": "^0.32.0"}, "files": ["dist"]}
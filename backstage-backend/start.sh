#!/bin/bash
# Carregar variáveis de ambiente do arquivo .env
if [ -f .env ]; then
  echo "Carregando variáveis do arquivo .env"
  set -a  # Automaticamente exporta todas as variáveis definidas
  source .env
  set +a
else
  echo "ERRO: Arquivo .env não encontrado!"
  echo "Por favor, crie o arquivo .env com as variáveis de ambiente necessárias."
  exit 1
fi

# Remover o container PostgreSQL existente se ele existir e estiver parado
docker rm postgres 2>/dev/null || true

# Iniciar container PostgreSQL
docker run -d --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=$POSTGRES_PASSWORD -e POSTGRES_USER=$POSTGRES_USER -e POSTGRES_DB=backstage postgres

# Instalar dependências e iniciar desenvolvimento
# yarn install
# yarn build
yarn start
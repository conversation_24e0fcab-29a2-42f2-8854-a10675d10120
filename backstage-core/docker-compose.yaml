volumes:
  dbback:

services:
  postgres:
    image: postgres
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - dbback:/var/lib/postgresql/data
    network_mode: host
  back-local:
    build:
      context: .
      dockerfile: DockerfileLocal
    environment:
      - POSTGRES_HOST=localhost
      - POSTGRES_PORT=5432
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - BITBUCKET_SERVER_USERNAME=backstage
      - BITBUCKET_SERVER_PASSWORD=${BITBUCKET_SERVER_PASSWORD}
      - LDAP_SECRET=${LDAP_SECRET}
      - AUTH_OKTA_CLIENT_ID=${AUTH_OKTA_CLIENT_ID}
      - AUTH_OKTA_CLIENT_SECRET=${AUTH_OKTA_CLIENT_SECRET}
      - AUTH_OKTA_URL=${AUTH_OKTA_URL}
      - BACKEND_SECRET=${BACKEND_SECRET}
      - K3S_SONAR_TOKEN=${K3S_SONAR_TOKEN}
      - OCP_SONAR_TOKEN=${OCP_SONAR_TOKEN}
      - EKS_BACKOFFICE_DEV=${EKS_BACKOFFICE_DEV}
      - EKS_BACKOFFICE_PRD=${EKS_BACKOFFICE_PRD}
      - SONAR_TOKEN=${SONAR_TOKEN}
      - BAMBOO_LOGIN=${BAMBOO_LOGIN}
      - GRAFANA_TOKEN=${GRAFANA_TOKEN}
      - APIM_LOGIN=${APIM_LOGIN}
    network_mode: host
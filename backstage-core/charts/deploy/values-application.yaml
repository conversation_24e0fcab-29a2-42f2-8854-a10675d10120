global:
  imageRegistry: "116762053366.dkr.ecr.us-east-1.amazonaws.com"
backstage:
  image:
    repository: engie-backstage
    tag: __REPO__-__VERSION__
    pullPolicy: IfNotPresent
  extraEnvVarsSecrets: ["backstage"]
  extraAppConfig: 
    - filename: app-config.yaml
      configMapRef: config
ingress:
  enabled: true
  host: "__HOSTNAME__"
  className: "nginx"
  tls:
    enabled: true
    secretName: "dns-tls"
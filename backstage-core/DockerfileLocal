FROM template/engie-devops:apim

WORKDIR /app

COPY package.json yarn.lock ./

WORKDIR /app/packages/app
COPY packages/app/package.json /app/packages/app

WORKDIR /app/packages/backend
COPY packages/backend/package.json /app/packages/backend

ENV NODE_OPTIONS=--no-node-snapshot

# WORKDIR /app/plugins/bamboo/
# COPY plugins/bamboo/package.json /app/plugins/bamboo

WORKDIR /app

# RUN --mount=type=cache,target=/var/cache/yarn \
#     yarn install -g

COPY . .

# RUN --mount=type=cache,target=/var/cache/yarn \ 
#     yarn install --immutable


EXPOSE 7007
EXPOSE 3000

CMD yarn run dev
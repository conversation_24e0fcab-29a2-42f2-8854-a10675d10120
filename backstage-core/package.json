{"name": "backstage-engie", "version": "1.0.0", "private": true, "engines": {"node": "20 || 22"}, "scripts": {"dev": "concurrently \"cd ../frontend && yarn start\" \"cd ../backend && yarn start\"", "build:plugins": "yarn --cwd plugins/apim build && yarn --cwd plugins/bamboo build", "prepare": "yarn build:plugins", "tsc": "tsc", "tsc:full": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> false --incremental false", "lint": "backstage-cli repo lint"}, "workspaces": {"packages": ["plugins/*"]}, "devDependencies": {"@backstage/cli": "^0.32.0", "concurrently": "^8.2.2", "prettier": "^2.3.2", "typescript": "~5.4.0"}, "resolutions": {"@types/react": "^18", "@types/react-dom": "^18"}, "prettier": "@spotify/prettier-config", "packageManager": "yarn@4.4.1"}
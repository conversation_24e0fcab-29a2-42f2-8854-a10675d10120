FROM node:20-bookworm-slim

WORKDIR /app

# Instalar dependências necessárias
RUN apt-get update && \
  apt-get install -y --no-install-recommends \
  python3 g++ build-essential git && \
  rm -rf /var/lib/apt/lists/*

# Copiar arquivos de configuração do yarn
COPY package.json yarn.lock ./
COPY .yarn ./.yarn
COPY .yarnrc.yml ./

# Copiar plugins e configuração do TypeScript
COPY plugins plugins/
COPY tsconfig.json ./

# Configuração do ambiente
ENV NODE_OPTIONS=--no-node-snapshot

# Instalar dependências
RUN yarn install

# Criar diretório dist-types para os arquivos de declaração
RUN mkdir -p dist-types/plugins/apim/src dist-types/plugins/bamboo/src

# Gerar arquivos de declaração TypeScript
RUN npx tsc --project tsconfig.json

# Construir plugins
RUN yarn build:plugins

# Expor porta (se necessário)
EXPOSE 7007

# Comando para manter o container rodando
<PERSON> ["tail", "-f", "/dev/null"]
{"include": ["plugins/*/src", "plugins/*/config.d.ts", "plugins/*/dev", "plugins/*/migrations"], "exclude": ["node_modules"], "compilerOptions": {"outDir": "dist-types", "rootDir": ".", "composite": true, "declaration": true, "declarationMap": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "jsx": "react", "module": "esnext", "moduleResolution": "node", "target": "es2019", "lib": ["DOM", "DOM.Iterable", "ESNext"]}}
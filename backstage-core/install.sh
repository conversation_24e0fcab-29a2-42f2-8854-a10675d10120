#!/bin/bash
set -e

echo "Instalando dependências do projeto principal..."
npm install

echo "Instalando dependências e construindo os plugins..."
cd plugins/apim && npm install && npm run build
cd ../..
cd plugins/bamboo && npm install && npm run build
cd ../..

echo "Instalando dependências do frontend..."
cd frontend && npm install
cd ..

echo "Instalando dependências do backend..."
cd backend && npm install
cd ..

echo "Criando arquivo .env padrão..."
cat > .env << EOL
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
BACKEND_SECRET=secret123
BITBUCKET_SERVER_USERNAME=backstage
BITBUCKET_SERVER_PASSWORD=backstage
LDAP_SECRET=secret
AUTH_OKTA_CLIENT_ID=okta_client_id
AUTH_OKTA_CLIENT_SECRET=okta_client_secret
AUTH_OKTA_URL=https://your-okta-domain/oauth2/default
K3S_SONAR_TOKEN=sonar_token
OCP_SONAR_TOKEN=ocp_token
EKS_BACKOFFICE_DEV=eks_dev_token
EKS_BACKOFFICE_PRD=eks_prd_token
SONAR_TOKEN=sonar_token
BAMBOO_LOGIN=bamboo_login
GRAFANA_TOKEN=grafana_token
APIM_LOGIN=apim_login
PERMISSION_SERVICE_USERNAME=admin
PERMISSION_SERVICE_PASSWORD=admin
EOL

echo "Instalação concluída com sucesso!" 
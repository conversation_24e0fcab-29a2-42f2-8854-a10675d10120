import React from 'react';
type Build = {
    results: {
        result: [
            {
                buildNumber: Number;
                buildState: String;
                key: String;
            }
        ];
    };
};
export declare const DenseTable: (builds: Build | undefined, urlBamboo: String) => React.JSX.Element;
export declare const BambooFetchComponent: (keyParam: String, urlBackstage: String, urlBamboo: String) => React.JSX.Element;
type Deploy = {
    deploymentProject: {
        description: String;
    };
    environmentStatuses: [
        {
            environment: {
                name: String;
                id: Number;
            };
            deploymentResult: {
                deploymentVersion: {
                    name: String;
                    planBranchName: String;
                };
                deploymentState: String;
            };
        }
    ];
};
export declare const DeploysTable: (deploys: Deploy[], urlBamboo: String) => React.JSX.Element;
export declare const DeployFetchComponent: (keyParam: String, urlBackstage: String, urlBamboo: String) => React.JSX.Element;
export {};
//# sourceMappingURL=BambooFetchComponent.d.ts.map
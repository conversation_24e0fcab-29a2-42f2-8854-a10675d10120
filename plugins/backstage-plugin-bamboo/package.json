{"name": "@internal/backstage-plugin-bamboo", "version": "0.1.0", "main": "dist/index.esm.js", "types": "dist/index.d.ts", "license": "Apache-2.0", "private": true, "publishConfig": {"access": "public", "main": "dist/index.esm.js", "types": "dist/index.d.ts"}, "backstage": {"role": "frontend-plugin"}, "sideEffects": false, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack", "commit": "git add . && git-cz && git push"}, "dependencies": {"@backstage/core-components": "^0.17.1", "@backstage/core-plugin-api": "^1.10.6", "@backstage/theme": "^0.6.5", "@material-ui/core": "^4.9.13", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.61", "git-cz": "^4.9.0", "react-use": "^17.2.4"}, "peerDependencies": {"react": "^16.13.1 || ^17.0.0 || ^18.0.0"}, "devDependencies": {"@backstage/cli": "^0.32.0", "@backstage/core-app-api": "^1.16.1", "@backstage/dev-utils": "^1.1.9", "@backstage/test-utils": "^1.7.7", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "msw": "^1.0.0", "react": "^16.13.1 || ^17.0.0 || ^18.0.0"}, "files": ["dist", "config.d.ts"], "configSchema": "config.d.ts"}
import React from 'react';
import { Table, TableColumn, Progress, ResponseErrorPanel } from '@backstage/core-components';
import useAsync from 'react-use/lib/useAsync';
import { Link } from 'react-router-dom'

type Build = {
  results: {
    result: [{
      buildNumber: Number,
      buildState: String,
      key: String
    }]
  }
}

export const DenseTable = (builds: Build | undefined, urlBamboo: String) => {

  const columns: TableColumn[] = [
    { title: 'Build', field: 'buildNumber' },
    { title: 'Status', field: 'buildState' },
    { title: 'Link do Build', field: 'link' },
  ];

  const data = builds?.results.result.slice(0,5).map(build => {
    return {
      buildNumber: `${build.buildNumber}`,
      buildState: `${build.buildState}`,
      link: <Link target='_blank' style={{color: 'blue'}} to={urlBamboo + `/browse/${build.key}`}>Link do Build</Link>
    };
  });


  return (
    <Table
      title="Últimos  5 Builds da Aplicação"
      options={{ search: false, paging: false }}
      columns={columns}
      data={data || []}
    />
  );
};

export const BambooFetchComponent = (keyParam: String, urlBackstage: String, urlBamboo: String) => {

  const { value, loading, error } = useAsync(async (): Promise<Build> => {
    return fetch(urlBackstage + `/api/proxy/bamboo/latest/result/` + keyParam)
      .then(response => {
        return response.json() as Promise<Build>
      })
  });

  if (loading) {
    return <Progress />;
  } else if (error) {
    return <ResponseErrorPanel error={error} />;
  }

  return DenseTable(value, urlBamboo);
};

type Deploy = {
  deploymentProject: {
    description: String
  }
  environmentStatuses: [{
    environment: {
      name: String,
      id: Number
    },
    deploymentResult: {
      deploymentVersion: {
        name: String,
        planBranchName: String
      }
      deploymentState: String
    },
  }]
}

export const DeploysTable = (deploys: Deploy[], urlBamboo: String) => {

  const columns: TableColumn[] = [
    { title: 'Ambiente', field: 'environment' },
    { title: 'Release', field: 'release' },
    { title: 'Branch', field: 'branch' },
    { title: 'Status Deploy', field: 'status' },
    { title: 'Link Deploy', field: 'link' },
  ];

  const data = deploys[0].environmentStatuses.map(deploy => {
    return {
      environment: `${deploy.environment.name}`,
      release: `${deploy.deploymentResult?.deploymentVersion.name}`,
      branch: `${deploy.deploymentResult?.deploymentVersion.planBranchName}`,
      status: `${deploy.deploymentResult?.deploymentState}`,
      link: <Link target='_blank' style={{color: 'blue'}} to={urlBamboo + `/deploy/viewEnvironment.action?id=${deploy.environment.id}`}>Release</Link>,
    };
  });

  return (
    <Table
      title="Versões do Ambiente"
      options={{ search: false, paging: false }}
      columns={columns}
      data={data}
    />
  );
};


export const DeployFetchComponent = (keyParam: String, urlBackstage: String, urlBamboo: String) => {

  const { value, loading, error } = useAsync(async (): Promise<Deploy[]> => {
    return fetch(urlBackstage + `/api/proxy/bamboo/latest/deploy/dashboard/` + keyParam)
      .then(response => {
        return response.json() as Promise<Deploy[]>
      })
  }, []);

  if (loading) {
    return <Progress />;
  } else if (error) {
    return <ResponseErrorPanel error={error} />;
  }

  return DeploysTable(value || [], urlBamboo);
};

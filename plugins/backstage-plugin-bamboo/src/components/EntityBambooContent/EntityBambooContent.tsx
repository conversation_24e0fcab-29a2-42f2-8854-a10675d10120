import React from 'react';
import { Typography, Grid } from '@material-ui/core';
import {
    InfoCard,
    Content,
    ContentHeader,
    SupportButton,
} from '@backstage/core-components';
import { BambooFetchComponent } from '../BambooFetchComponent';
import { DeployFetchComponent } from '../BambooFetchComponent/BambooFetchComponent';
import { useEntity } from "@backstage/plugin-catalog-react"
import { useApi, configApiRef } from '@backstage/core-plugin-api';
import { EmptyState } from '@backstage/core-components';

export const EntityBambooContent = () => {

    const { entity } = useEntity();
    const config = useApi(configApiRef);

    const idBuild: String = entity.metadata.annotations?.['engie.bamboo/build-key'] || "";
    const idDeploy = entity.metadata.annotations?.['engie.bamboo/deploy-key'] || "";

    const urlBackstage = config.getConfig('backend').getString('baseUrl') || "";
    const urlBamboo = config.getConfig('bamboo').getString('baseUrl') || "";


    if (idBuild != "" || idDeploy != "") {
        return (
            <Content>
                <ContentHeader title="Informações da Pipeline">
                    <SupportButton>Ferramenta de CI/CD da Engie.</SupportButton>
                </ContentHeader>
                <Grid container spacing={3} direction="column">
                    <Grid item>
                        <InfoCard title="Bamboo">
                            <Typography variant="body1">
                                Ferramenta de CI/CD da Engie
                            </Typography>
                        </InfoCard>
                    </Grid>
                    <Grid item>
                        {BambooFetchComponent(idBuild, urlBackstage, urlBamboo)}
                    </Grid>
                    <Grid item>
                        {DeployFetchComponent(idDeploy, urlBackstage, urlBamboo)}
                    </Grid>
                </Grid>
            </Content>
        );
    } else {
        return (
            <EmptyState
                missing="info"
                title="Sem Informações para essa Aplicação"
                description={`Revise o catalogo-info da aplicação adicionando as tags: engie.bamboo/build-key e engie.bamboo/deploy-key`} />
        );
    }
};
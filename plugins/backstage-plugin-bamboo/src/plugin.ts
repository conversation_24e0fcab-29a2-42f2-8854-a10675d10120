import {
  createPlugin,
  createRoutableExtension,
} from '@backstage/core-plugin-api';

import { rootRouteRef } from './routes';

export const bambooPlugin = createPlugin({
  id: 'bamboo',
  routes: {
    root: rootRouteRef,
  },
});

export const BambooPage = bambooPlugin.provide(
  createRoutableExtension({
    name: 'BambooPage',
    component: () =>
      import('./components/EntityBambooContent').then(m => m.EntityBambooContent),
    mountPoint: rootRouteRef,
  }),
);

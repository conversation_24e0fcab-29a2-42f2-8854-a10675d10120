import React from 'react';
import { Grid, Typography } from '@material-ui/core';
import { Content, ContentHeader, SupportButton, InfoCard, EmptyState } from '@backstage/core-components';
import { BambooFetchComponent, DeployFetchComponent } from '../BambooFetchComponent/BambooFetchComponent.esm.js';
import { useEntity } from '@backstage/plugin-catalog-react';
import { useApi, configApiRef } from '@backstage/core-plugin-api';

const EntityBambooContent = () => {
  const { entity } = useEntity();
  const config = useApi(configApiRef);
  const idBuild = entity.metadata.annotations?.["engie.bamboo/build-key"] || "";
  const idDeploy = entity.metadata.annotations?.["engie.bamboo/deploy-key"] || "";
  const urlBackstage = config.getConfig("backend").getString("baseUrl") || "";
  const urlBamboo = config.getConfig("bamboo").getString("baseUrl") || "";
  if (idBuild != "" || idDeploy != "") {
    return /* @__PURE__ */ React.createElement(Content, null, /* @__PURE__ */ React.createElement(ContentHeader, { title: "Informa\xE7\xF5es da Pipeline" }, /* @__PURE__ */ React.createElement(SupportButton, null, "Ferramenta de CI/CD da Engie.")), /* @__PURE__ */ React.createElement(Grid, { container: true, spacing: 3, direction: "column" }, /* @__PURE__ */ React.createElement(Grid, { item: true }, /* @__PURE__ */ React.createElement(InfoCard, { title: "Bamboo" }, /* @__PURE__ */ React.createElement(Typography, { variant: "body1" }, "Ferramenta de CI/CD da Engie"))), /* @__PURE__ */ React.createElement(Grid, { item: true }, BambooFetchComponent(idBuild, urlBackstage, urlBamboo)), /* @__PURE__ */ React.createElement(Grid, { item: true }, DeployFetchComponent(idDeploy, urlBackstage, urlBamboo))));
  } else {
    return /* @__PURE__ */ React.createElement(
      EmptyState,
      {
        missing: "info",
        title: "Sem Informa\xE7\xF5es para essa Aplica\xE7\xE3o",
        description: `Revise o catalogo-info da aplica\xE7\xE3o adicionando as tags: engie.bamboo/build-key e engie.bamboo/deploy-key`
      }
    );
  }
};

export { EntityBambooContent };
//# sourceMappingURL=EntityBambooContent.esm.js.map

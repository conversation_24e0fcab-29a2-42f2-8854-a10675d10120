{"version": 3, "file": "EntityBambooContent.esm.js", "sources": ["../../../src/components/EntityBambooContent/EntityBambooContent.tsx"], "sourcesContent": ["import React from 'react';\nimport { Typography, Grid } from '@material-ui/core';\nimport {\n    InfoCard,\n    Content,\n    ContentHeader,\n    SupportButton,\n} from '@backstage/core-components';\nimport { BambooFetchComponent } from '../BambooFetchComponent';\nimport { DeployFetchComponent } from '../BambooFetchComponent/BambooFetchComponent';\nimport { useEntity } from \"@backstage/plugin-catalog-react\"\nimport { useApi, configApiRef } from '@backstage/core-plugin-api';\nimport { EmptyState } from '@backstage/core-components';\n\nexport const EntityBambooContent = () => {\n\n    const { entity } = useEntity();\n    const config = useApi(configApiRef);\n\n    const idBuild: String = entity.metadata.annotations?.['engie.bamboo/build-key'] || \"\";\n    const idDeploy = entity.metadata.annotations?.['engie.bamboo/deploy-key'] || \"\";\n\n    const urlBackstage = config.getConfig('backend').getString('baseUrl') || \"\";\n    const urlBamboo = config.getConfig('bamboo').getString('baseUrl') || \"\";\n\n\n    if (idBuild != \"\" || idDeploy != \"\") {\n        return (\n            <Content>\n                <ContentHeader title=\"Informações da Pipeline\">\n                    <SupportButton>Ferramenta de CI/CD da Engie.</SupportButton>\n                </ContentHeader>\n                <Grid container spacing={3} direction=\"column\">\n                    <Grid item>\n                        <InfoCard title=\"Bamboo\">\n                            <Typography variant=\"body1\">\n                                Ferramenta de CI/CD da Engie\n                            </Typography>\n                        </InfoCard>\n                    </Grid>\n                    <Grid item>\n                        {BambooFetchComponent(idBuild, urlBackstage, urlBamboo)}\n                    </Grid>\n                    <Grid item>\n                        {DeployFetchComponent(idDeploy, urlBackstage, urlBamboo)}\n                    </Grid>\n                </Grid>\n            </Content>\n        );\n    } else {\n        return (\n            <EmptyState\n                missing=\"info\"\n                title=\"Sem Informações para essa Aplicação\"\n                description={`Revise o catalogo-info da aplicação adicionando as tags: engie.bamboo/build-key e engie.bamboo/deploy-key`} />\n        );\n    }\n};"], "names": [], "mappings": ";;;;;;;AAcO,MAAM,sBAAsB,MAAM;AAErC,EAAM,MAAA,EAAE,MAAO,EAAA,GAAI,SAAU,EAAA;AAC7B,EAAM,MAAA,MAAA,GAAS,OAAO,YAAY,CAAA;AAElC,EAAA,MAAM,OAAkB,GAAA,MAAA,CAAO,QAAS,CAAA,WAAA,GAAc,wBAAwB,CAAK,IAAA,EAAA;AACnF,EAAA,MAAM,QAAW,GAAA,MAAA,CAAO,QAAS,CAAA,WAAA,GAAc,yBAAyB,CAAK,IAAA,EAAA;AAE7E,EAAA,MAAM,eAAe,MAAO,CAAA,SAAA,CAAU,SAAS,CAAE,CAAA,SAAA,CAAU,SAAS,CAAK,IAAA,EAAA;AACzE,EAAA,MAAM,YAAY,MAAO,CAAA,SAAA,CAAU,QAAQ,CAAE,CAAA,SAAA,CAAU,SAAS,CAAK,IAAA,EAAA;AAGrE,EAAI,IAAA,OAAA,IAAW,EAAM,IAAA,QAAA,IAAY,EAAI,EAAA;AACjC,IACI,uBAAA,KAAA,CAAA,aAAA,CAAC,OACG,EAAA,IAAA,kBAAA,KAAA,CAAA,aAAA,CAAC,aAAc,EAAA,EAAA,KAAA,EAAM,mDAChB,KAAA,CAAA,aAAA,CAAA,aAAA,EAAA,IAAA,EAAc,+BAA6B,CAChD,CACA,kBAAA,KAAA,CAAA,aAAA,CAAC,QAAK,SAAS,EAAA,IAAA,EAAC,OAAS,EAAA,CAAA,EAAG,SAAU,EAAA,QAAA,EAAA,kBACjC,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA,EAAK,IAAI,EAAA,IAAA,EAAA,kBACL,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA,EAAS,KAAM,EAAA,QAAA,EAAA,sCACX,UAAW,EAAA,EAAA,OAAA,EAAQ,OAAQ,EAAA,EAAA,8BAE5B,CACJ,CACJ,CACA,kBAAA,KAAA,CAAA,aAAA,CAAC,IAAK,EAAA,EAAA,IAAA,EAAI,IACL,EAAA,EAAA,oBAAA,CAAqB,OAAS,EAAA,YAAA,EAAc,SAAS,CAC1D,CAAA,kBACC,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA,EAAK,IAAI,EAAA,IAAA,EAAA,EACL,oBAAqB,CAAA,QAAA,EAAU,YAAc,EAAA,SAAS,CAC3D,CACJ,CACJ,CAAA;AAAA,GAED,MAAA;AACH,IACI,uBAAA,KAAA,CAAA,aAAA;AAAA,MAAC,UAAA;AAAA,MAAA;AAAA,QACG,OAAQ,EAAA,MAAA;AAAA,QACR,KAAM,EAAA,iDAAA;AAAA,QACN,WAAa,EAAA,CAAA,+GAAA;AAAA;AAAA,KAA6G;AAAA;AAG1I;;;;"}
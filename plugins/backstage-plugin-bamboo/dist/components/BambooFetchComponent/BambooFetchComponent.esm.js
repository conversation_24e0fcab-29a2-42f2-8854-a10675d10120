import React from 'react';
import { Progress, ResponseErrorPanel, Table } from '@backstage/core-components';
import useAsync from 'react-use/lib/useAsync';
import { Link } from 'react-router-dom';

const DenseTable = (builds, urlBamboo) => {
  const columns = [
    { title: "Build", field: "buildNumber" },
    { title: "Status", field: "buildState" },
    { title: "Link do Build", field: "link" }
  ];
  const data = builds?.results.result.slice(0, 5).map((build) => {
    return {
      buildNumber: `${build.buildNumber}`,
      buildState: `${build.buildState}`,
      link: /* @__PURE__ */ React.createElement(Link, { target: "_blank", style: { color: "blue" }, to: urlBamboo + `/browse/${build.key}` }, "Link do Build")
    };
  });
  return /* @__PURE__ */ React.createElement(
    Table,
    {
      title: "\xDAltimos  5 Builds da Aplica\xE7\xE3o",
      options: { search: false, paging: false },
      columns,
      data: data || []
    }
  );
};
const BambooFetchComponent = (keyParam, urlBackstage, urlBamboo) => {
  const { value, loading, error } = useAsync(async () => {
    return fetch(urlBackstage + `/api/proxy/bamboo/latest/result/` + keyParam).then((response) => {
      return response.json();
    });
  });
  if (loading) {
    return /* @__PURE__ */ React.createElement(Progress, null);
  } else if (error) {
    return /* @__PURE__ */ React.createElement(ResponseErrorPanel, { error });
  }
  return DenseTable(value, urlBamboo);
};
const DeploysTable = (deploys, urlBamboo) => {
  const columns = [
    { title: "Ambiente", field: "environment" },
    { title: "Release", field: "release" },
    { title: "Branch", field: "branch" },
    { title: "Status Deploy", field: "status" },
    { title: "Link Deploy", field: "link" }
  ];
  const data = deploys[0].environmentStatuses.map((deploy) => {
    return {
      environment: `${deploy.environment.name}`,
      release: `${deploy.deploymentResult?.deploymentVersion.name}`,
      branch: `${deploy.deploymentResult?.deploymentVersion.planBranchName}`,
      status: `${deploy.deploymentResult?.deploymentState}`,
      link: /* @__PURE__ */ React.createElement(Link, { target: "_blank", style: { color: "blue" }, to: urlBamboo + `/deploy/viewEnvironment.action?id=${deploy.environment.id}` }, "Release")
    };
  });
  return /* @__PURE__ */ React.createElement(
    Table,
    {
      title: "Vers\xF5es do Ambiente",
      options: { search: false, paging: false },
      columns,
      data
    }
  );
};
const DeployFetchComponent = (keyParam, urlBackstage, urlBamboo) => {
  const { value, loading, error } = useAsync(async () => {
    return fetch(urlBackstage + `/api/proxy/bamboo/latest/deploy/dashboard/` + keyParam).then((response) => {
      return response.json();
    });
  }, []);
  if (loading) {
    return /* @__PURE__ */ React.createElement(Progress, null);
  } else if (error) {
    return /* @__PURE__ */ React.createElement(ResponseErrorPanel, { error });
  }
  return DeploysTable(value || [], urlBamboo);
};

export { BambooFetchComponent, DenseTable, DeployFetchComponent, DeploysTable };
//# sourceMappingURL=BambooFetchComponent.esm.js.map

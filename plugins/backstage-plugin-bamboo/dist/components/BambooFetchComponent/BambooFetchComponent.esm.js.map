{"version": 3, "file": "BambooFetchComponent.esm.js", "sources": ["../../../src/components/BambooFetchComponent/BambooFetchComponent.tsx"], "sourcesContent": ["import React from 'react';\nimport { Table, TableColumn, Progress, ResponseErrorPanel } from '@backstage/core-components';\nimport useAsync from 'react-use/lib/useAsync';\nimport { Link } from 'react-router-dom'\n\ntype Build = {\n  results: {\n    result: [{\n      buildNumber: Number,\n      buildState: String,\n      key: String\n    }]\n  }\n}\n\nexport const DenseTable = (builds: Build | undefined, urlBamboo: String) => {\n\n  const columns: TableColumn[] = [\n    { title: 'Build', field: 'buildNumber' },\n    { title: 'Status', field: 'buildState' },\n    { title: 'Link do Build', field: 'link' },\n  ];\n\n  const data = builds?.results.result.slice(0,5).map(build => {\n    return {\n      buildNumber: `${build.buildNumber}`,\n      buildState: `${build.buildState}`,\n      link: <Link target='_blank' style={{color: 'blue'}} to={urlBamboo + `/browse/${build.key}`}>Link do Build</Link>\n    };\n  });\n\n\n  return (\n    <Table\n      title=\"Últimos  5 Builds da Aplicação\"\n      options={{ search: false, paging: false }}\n      columns={columns}\n      data={data || []}\n    />\n  );\n};\n\nexport const BambooFetchComponent = (keyParam: String, urlBackstage: String, urlBamboo: String) => {\n\n  const { value, loading, error } = useAsync(async (): Promise<Build> => {\n    return fetch(urlBackstage + `/api/proxy/bamboo/latest/result/` + keyParam)\n      .then(response => {\n        return response.json() as Promise<Build>\n      })\n  });\n\n  if (loading) {\n    return <Progress />;\n  } else if (error) {\n    return <ResponseErrorPanel error={error} />;\n  }\n\n  return DenseTable(value, urlBamboo);\n};\n\ntype Deploy = {\n  deploymentProject: {\n    description: String\n  }\n  environmentStatuses: [{\n    environment: {\n      name: String,\n      id: Number\n    },\n    deploymentResult: {\n      deploymentVersion: {\n        name: String,\n        planBranchName: String\n      }\n      deploymentState: String\n    },\n  }]\n}\n\nexport const DeploysTable = (deploys: Deploy[], urlBamboo: String) => {\n\n  const columns: TableColumn[] = [\n    { title: 'Ambiente', field: 'environment' },\n    { title: 'Release', field: 'release' },\n    { title: 'Branch', field: 'branch' },\n    { title: 'Status Deploy', field: 'status' },\n    { title: 'Link Deploy', field: 'link' },\n  ];\n\n  const data = deploys[0].environmentStatuses.map(deploy => {\n    return {\n      environment: `${deploy.environment.name}`,\n      release: `${deploy.deploymentResult?.deploymentVersion.name}`,\n      branch: `${deploy.deploymentResult?.deploymentVersion.planBranchName}`,\n      status: `${deploy.deploymentResult?.deploymentState}`,\n      link: <Link target='_blank' style={{color: 'blue'}} to={urlBamboo + `/deploy/viewEnvironment.action?id=${deploy.environment.id}`}>Release</Link>,\n    };\n  });\n\n  return (\n    <Table\n      title=\"Versões do Ambiente\"\n      options={{ search: false, paging: false }}\n      columns={columns}\n      data={data}\n    />\n  );\n};\n\n\nexport const DeployFetchComponent = (keyParam: String, urlBackstage: String, urlBamboo: String) => {\n\n  const { value, loading, error } = useAsync(async (): Promise<Deploy[]> => {\n    return fetch(urlBackstage + `/api/proxy/bamboo/latest/deploy/dashboard/` + keyParam)\n      .then(response => {\n        return response.json() as Promise<Deploy[]>\n      })\n  }, []);\n\n  if (loading) {\n    return <Progress />;\n  } else if (error) {\n    return <ResponseErrorPanel error={error} />;\n  }\n\n  return DeploysTable(value || [], urlBamboo);\n};\n"], "names": [], "mappings": ";;;;;AAea,MAAA,UAAA,GAAa,CAAC,MAAA,EAA2B,SAAsB,KAAA;AAE1E,EAAA,MAAM,OAAyB,GAAA;AAAA,IAC7B,EAAE,KAAA,EAAO,OAAS,EAAA,KAAA,EAAO,aAAc,EAAA;AAAA,IACvC,EAAE,KAAA,EAAO,QAAU,EAAA,KAAA,EAAO,YAAa,EAAA;AAAA,IACvC,EAAE,KAAA,EAAO,eAAiB,EAAA,KAAA,EAAO,MAAO;AAAA,GAC1C;AAEA,EAAM,MAAA,IAAA,GAAO,QAAQ,OAAQ,CAAA,MAAA,CAAO,MAAM,CAAE,EAAA,CAAC,CAAE,CAAA,GAAA,CAAI,CAAS,KAAA,KAAA;AAC1D,IAAO,OAAA;AAAA,MACL,WAAA,EAAa,CAAG,EAAA,KAAA,CAAM,WAAW,CAAA,CAAA;AAAA,MACjC,UAAA,EAAY,CAAG,EAAA,KAAA,CAAM,UAAU,CAAA,CAAA;AAAA,MAC/B,sBAAO,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA,EAAK,MAAO,EAAA,QAAA,EAAS,OAAO,EAAC,KAAA,EAAO,MAAM,EAAA,EAAG,IAAI,SAAY,GAAA,CAAA,QAAA,EAAW,KAAM,CAAA,GAAG,MAAI,eAAa;AAAA,KAC3G;AAAA,GACD,CAAA;AAGD,EACE,uBAAA,KAAA,CAAA,aAAA;AAAA,IAAC,KAAA;AAAA,IAAA;AAAA,MACC,KAAM,EAAA,yCAAA;AAAA,MACN,OAAS,EAAA,EAAE,MAAQ,EAAA,KAAA,EAAO,QAAQ,KAAM,EAAA;AAAA,MACxC,OAAA;AAAA,MACA,IAAA,EAAM,QAAQ;AAAC;AAAA,GACjB;AAEJ;AAEO,MAAM,oBAAuB,GAAA,CAAC,QAAkB,EAAA,YAAA,EAAsB,SAAsB,KAAA;AAEjG,EAAA,MAAM,EAAE,KAAO,EAAA,OAAA,EAAS,KAAM,EAAA,GAAI,SAAS,YAA4B;AACrE,IAAA,OAAO,MAAM,YAAe,GAAA,CAAA,gCAAA,CAAA,GAAqC,QAAQ,CAAA,CACtE,KAAK,CAAY,QAAA,KAAA;AAChB,MAAA,OAAO,SAAS,IAAK,EAAA;AAAA,KACtB,CAAA;AAAA,GACJ,CAAA;AAED,EAAA,IAAI,OAAS,EAAA;AACX,IAAA,2CAAQ,QAAS,EAAA,IAAA,CAAA;AAAA,aACR,KAAO,EAAA;AAChB,IAAO,uBAAA,KAAA,CAAA,aAAA,CAAC,sBAAmB,KAAc,EAAA,CAAA;AAAA;AAG3C,EAAO,OAAA,UAAA,CAAW,OAAO,SAAS,CAAA;AACpC;AAqBa,MAAA,YAAA,GAAe,CAAC,OAAA,EAAmB,SAAsB,KAAA;AAEpE,EAAA,MAAM,OAAyB,GAAA;AAAA,IAC7B,EAAE,KAAA,EAAO,UAAY,EAAA,KAAA,EAAO,aAAc,EAAA;AAAA,IAC1C,EAAE,KAAA,EAAO,SAAW,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACrC,EAAE,KAAA,EAAO,QAAU,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACnC,EAAE,KAAA,EAAO,eAAiB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC1C,EAAE,KAAA,EAAO,aAAe,EAAA,KAAA,EAAO,MAAO;AAAA,GACxC;AAEA,EAAA,MAAM,OAAO,OAAQ,CAAA,CAAC,CAAE,CAAA,mBAAA,CAAoB,IAAI,CAAU,MAAA,KAAA;AACxD,IAAO,OAAA;AAAA,MACL,WAAa,EAAA,CAAA,EAAG,MAAO,CAAA,WAAA,CAAY,IAAI,CAAA,CAAA;AAAA,MACvC,OAAS,EAAA,CAAA,EAAG,MAAO,CAAA,gBAAA,EAAkB,kBAAkB,IAAI,CAAA,CAAA;AAAA,MAC3D,MAAQ,EAAA,CAAA,EAAG,MAAO,CAAA,gBAAA,EAAkB,kBAAkB,cAAc,CAAA,CAAA;AAAA,MACpE,MAAQ,EAAA,CAAA,EAAG,MAAO,CAAA,gBAAA,EAAkB,eAAe,CAAA,CAAA;AAAA,MACnD,sBAAO,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA,EAAK,MAAO,EAAA,QAAA,EAAS,OAAO,EAAC,KAAA,EAAO,MAAM,EAAA,EAAG,IAAI,SAAY,GAAA,CAAA,kCAAA,EAAqC,OAAO,WAAY,CAAA,EAAE,MAAI,SAAO;AAAA,KAC3I;AAAA,GACD,CAAA;AAED,EACE,uBAAA,KAAA,CAAA,aAAA;AAAA,IAAC,KAAA;AAAA,IAAA;AAAA,MACC,KAAM,EAAA,wBAAA;AAAA,MACN,OAAS,EAAA,EAAE,MAAQ,EAAA,KAAA,EAAO,QAAQ,KAAM,EAAA;AAAA,MACxC,OAAA;AAAA,MACA;AAAA;AAAA,GACF;AAEJ;AAGO,MAAM,oBAAuB,GAAA,CAAC,QAAkB,EAAA,YAAA,EAAsB,SAAsB,KAAA;AAEjG,EAAA,MAAM,EAAE,KAAO,EAAA,OAAA,EAAS,KAAM,EAAA,GAAI,SAAS,YAA+B;AACxE,IAAA,OAAO,MAAM,YAAe,GAAA,CAAA,0CAAA,CAAA,GAA+C,QAAQ,CAAA,CAChF,KAAK,CAAY,QAAA,KAAA;AAChB,MAAA,OAAO,SAAS,IAAK,EAAA;AAAA,KACtB,CAAA;AAAA,GACL,EAAG,EAAE,CAAA;AAEL,EAAA,IAAI,OAAS,EAAA;AACX,IAAA,2CAAQ,QAAS,EAAA,IAAA,CAAA;AAAA,aACR,KAAO,EAAA;AAChB,IAAO,uBAAA,KAAA,CAAA,aAAA,CAAC,sBAAmB,KAAc,EAAA,CAAA;AAAA;AAG3C,EAAA,OAAO,YAAa,CAAA,KAAA,IAAS,EAAC,EAAG,SAAS,CAAA;AAC5C;;;;"}
{"version": 3, "file": "plugin.esm.js", "sources": ["../src/plugin.ts"], "sourcesContent": ["import {\n  createPlugin,\n  createRoutableExtension,\n} from '@backstage/core-plugin-api';\n\nimport { rootRouteRef } from './routes';\n\nexport const bambooPlugin = createPlugin({\n  id: 'bamboo',\n  routes: {\n    root: rootRouteRef,\n  },\n});\n\nexport const BambooPage = bambooPlugin.provide(\n  createRoutableExtension({\n    name: 'BambooPage',\n    component: () =>\n      import('./components/EntityBambooContent').then(m => m.EntityBambooContent),\n    mountPoint: rootRouteRef,\n  }),\n);\n"], "names": [], "mappings": ";;;AAOO,MAAM,eAAe,YAAa,CAAA;AAAA,EACvC,EAAI,EAAA,QAAA;AAAA,EACJ,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA;AAAA;AAEV,CAAC;AAEM,MAAM,aAAa,YAAa,CAAA,OAAA;AAAA,EACrC,uBAAwB,CAAA;AAAA,IACtB,IAAM,EAAA,YAAA;AAAA,IACN,SAAA,EAAW,MACT,OAAO,+CAAkC,EAAE,IAAK,CAAA,CAAA,CAAA,KAAK,EAAE,mBAAmB,CAAA;AAAA,IAC5E,UAAY,EAAA;AAAA,GACb;AACH;;;;"}
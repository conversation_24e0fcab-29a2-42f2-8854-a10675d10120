import { createPlugin, createRoutableExtension } from '@backstage/core-plugin-api';
import { rootRouteRef } from './routes.esm.js';

const bambooPlugin = createPlugin({
  id: "bamboo",
  routes: {
    root: rootRouteRef
  }
});
const BambooPage = bambooPlugin.provide(
  createRoutableExtension({
    name: "BambooPage",
    component: () => import('./components/EntityBambooContent/index.esm.js').then((m) => m.EntityBambooContent),
    mountPoint: rootRouteRef
  })
);

export { BambooPage, bambooPlugin };
//# sourceMappingURL=plugin.esm.js.map

{"version": 3, "file": "plugin.esm.js", "sources": ["../src/plugin.ts"], "sourcesContent": ["import {\n  createPlugin,\n  createRoutableExtension,\n} from '@backstage/core-plugin-api';\n\nimport { rootRouteRef } from './routes';\n\nexport const apimPlugin = createPlugin({\n  id: 'apim',\n  routes: {\n    root: rootRouteRef,\n  },\n});\n\nexport const ApimPage = apimPlugin.provide(\n  createRoutableExtension({\n    name: 'ApimPage',\n    component: () =>\n      import('./components/ApimComponent').then(m => m.ApimComponent),\n    mountPoint: rootRouteRef,\n  }),\n);\n"], "names": [], "mappings": ";;;AAOO,MAAM,aAAa,YAAa,CAAA;AAAA,EACrC,EAAI,EAAA,MAAA;AAAA,EACJ,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA;AAAA;AAEV,CAAC;AAEM,MAAM,WAAW,UAAW,CAAA,OAAA;AAAA,EACjC,uBAAwB,CAAA;AAAA,IACtB,IAAM,EAAA,UAAA;AAAA,IACN,SAAA,EAAW,MACT,OAAO,yCAA4B,EAAE,IAAK,CAAA,CAAA,CAAA,KAAK,EAAE,aAAa,CAAA;AAAA,IAChE,UAAY,EAAA;AAAA,GACb;AACH;;;;"}
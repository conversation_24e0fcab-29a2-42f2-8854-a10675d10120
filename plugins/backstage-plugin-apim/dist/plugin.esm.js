import { createPlugin, createRoutableExtension } from '@backstage/core-plugin-api';
import { rootRouteRef } from './routes.esm.js';

const apimPlugin = createPlugin({
  id: "apim",
  routes: {
    root: rootRouteRef
  }
});
const ApimPage = apimPlugin.provide(
  createRoutableExtension({
    name: "ApimPage",
    component: () => import('./components/ApimComponent/index.esm.js').then((m) => m.ApimComponent),
    mountPoint: rootRouteRef
  })
);

export { ApimPage, apimPlugin };
//# sourceMappingURL=plugin.esm.js.map

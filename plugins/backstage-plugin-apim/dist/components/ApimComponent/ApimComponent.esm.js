import React from 'react';
import { Grid, Typography } from '@material-ui/core';
import { Content, ContentHeader, SupportButton, InfoCard, EmptyState } from '@backstage/core-components';
import { ApimConsumidasFetchComponent } from '../ApimFetchComponent/ApimFetchComponent.esm.js';
import { useApi, configApiRef } from '@backstage/core-plugin-api';
import { useEntity } from '@backstage/plugin-catalog-react';

const ApimComponent = () => {
  const { entity } = useEntity();
  const config = useApi(configApiRef);
  const urlBackstage = config.getConfig("backend").getString("baseUrl") || "";
  const codeApplication = entity.metadata.annotations?.["engie.apim/code-application"] || "";
  if (codeApplication) {
    return /* @__PURE__ */ React.createElement(Content, null, /* @__PURE__ */ React.createElement(ContentHeader, { title: "API Management" }, /* @__PURE__ */ React.createElement(SupportButton, null, "Centraliza\xE7\xE3o das APIs disponibilizadas e Utilizadas pela Aplica\xE7\xE3o.")), /* @__PURE__ */ React.createElement(Grid, { container: true, spacing: 3, direction: "column" }, /* @__PURE__ */ React.createElement(Grid, { item: true }, /* @__PURE__ */ React.createElement(InfoCard, { title: "Minhas APIs" }, /* @__PURE__ */ React.createElement(Typography, { variant: "body1" }, "Centraliza\xE7\xE3o das APIs disponibilizadas e Utilizadas pela Aplica\xE7\xE3o."))), /* @__PURE__ */ React.createElement(Grid, { item: true }, ApimConsumidasFetchComponent(urlBackstage, codeApplication))));
  } else {
    return /* @__PURE__ */ React.createElement(
      EmptyState,
      {
        missing: "info",
        title: "Sem Informa\xE7\xF5es para essa Aplica\xE7\xE3o",
        description: `Revise o catalogo-info da aplica\xE7\xE3o adicionando as tags: engie.apim/code-application`
      }
    );
  }
};

export { ApimComponent };
//# sourceMappingURL=ApimComponent.esm.js.map

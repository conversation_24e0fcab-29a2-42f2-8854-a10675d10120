{"version": 3, "file": "ApimComponent.esm.js", "sources": ["../../../src/components/ApimComponent/ApimComponent.tsx"], "sourcesContent": ["import React from 'react';\nimport { Typography, Grid } from '@material-ui/core';\nimport {\n  InfoCard,\n  Content,\n  ContentHeader,\n  SupportButton,\n  EmptyState,\n} from '@backstage/core-components';\nimport { ApimConsumidasFetchComponent } from '../ApimFetchComponent';\nimport { configApiRef, useApi } from '@backstage/core-plugin-api';\nimport { useEntity } from \"@backstage/plugin-catalog-react\"\n\n\nexport const ApimComponent = () => {\n\n  const { entity } = useEntity();\n  const config = useApi(configApiRef);\n\n  const urlBackstage = config.getConfig('backend').getString('baseUrl') || \"\";\n  const codeApplication: string = entity.metadata.annotations?.['engie.apim/code-application'] || \"\";\n\n\n  if (codeApplication) {\n    return (\n      <Content>\n        <ContentHeader title=\"API Management\">\n          <SupportButton>Centralização das APIs disponibilizadas e Utilizadas pela Aplicação.</SupportButton>\n        </ContentHeader>\n        <Grid container spacing={3} direction=\"column\">\n          <Grid item>\n            <InfoCard title=\"Minhas APIs\">\n              <Typography variant=\"body1\">\n                Centralização das APIs disponibilizadas e Utilizadas pela Aplicação.\n              </Typography>\n            </InfoCard>\n          </Grid>\n          <Grid item>\n            {ApimConsumidasFetchComponent(urlBackstage, codeApplication)}\n          </Grid>\n        </Grid>\n      </Content>\n    )\n  } else {\n    return (\n      <EmptyState\n        missing=\"info\"\n        title=\"Sem Informações para essa Aplicação\"\n        description={`Revise o catalogo-info da aplicação adicionando as tags: engie.apim/code-application`} />\n    );\n  }\n\n};"], "names": [], "mappings": ";;;;;;;AAcO,MAAM,gBAAgB,MAAM;AAEjC,EAAM,MAAA,EAAE,MAAO,EAAA,GAAI,SAAU,EAAA;AAC7B,EAAM,MAAA,MAAA,GAAS,OAAO,YAAY,CAAA;AAElC,EAAA,MAAM,eAAe,MAAO,CAAA,SAAA,CAAU,SAAS,CAAE,CAAA,SAAA,CAAU,SAAS,CAAK,IAAA,EAAA;AACzE,EAAA,MAAM,eAA0B,GAAA,MAAA,CAAO,QAAS,CAAA,WAAA,GAAc,6BAA6B,CAAK,IAAA,EAAA;AAGhG,EAAA,IAAI,eAAiB,EAAA;AACnB,IACE,uBAAA,KAAA,CAAA,aAAA,CAAC,+BACE,KAAA,CAAA,aAAA,CAAA,aAAA,EAAA,EAAc,OAAM,gBACnB,EAAA,kBAAA,KAAA,CAAA,aAAA,CAAC,qBAAc,kFAAoE,CACrF,mBACC,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA,EAAK,WAAS,IAAC,EAAA,OAAA,EAAS,GAAG,SAAU,EAAA,QAAA,EAAA,sCACnC,IAAK,EAAA,EAAA,IAAA,EAAI,wBACP,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA,EAAS,OAAM,aACd,EAAA,kBAAA,KAAA,CAAA,aAAA,CAAC,cAAW,OAAQ,EAAA,OAAA,EAAA,EAAQ,kFAE5B,CACF,CACF,mBACC,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA,EAAK,MAAI,IACP,EAAA,EAAA,4BAAA,CAA6B,cAAc,eAAe,CAC7D,CACF,CACF,CAAA;AAAA,GAEG,MAAA;AACL,IACE,uBAAA,KAAA,CAAA,aAAA;AAAA,MAAC,UAAA;AAAA,MAAA;AAAA,QACC,OAAQ,EAAA,MAAA;AAAA,QACR,KAAM,EAAA,iDAAA;AAAA,QACN,WAAa,EAAA,CAAA,0FAAA;AAAA;AAAA,KAAwF;AAAA;AAI7G;;;;"}
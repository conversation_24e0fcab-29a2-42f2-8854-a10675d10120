{"version": 3, "file": "ApimFetchComponent.esm.js", "sources": ["../../../src/components/ApimFetchComponent/ApimFetchComponent.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, Progress, ResponseErrorPanel, Table, TableColumn } from '@backstage/core-components';\nimport useAsync from 'react-use/lib/useAsync';\n\ntype DenseTableProps = {\n  application: Application | undefined,\n  urlBackstage: string | undefined\n};\n\ntype Application = {\n  name: string | undefined,\n  state: string | undefined,\n  apis: Api[] | undefined\n};\n\ntype APIId = {\n  apiId: string,\n  enabled: boolean\n};\n\ntype Api = {\n  name: string,\n  id: string,\n  uri: string\n};\n\nconst DenseTable = ({ application, urlBackstage }: DenseTableProps) => {\n\n  const columns: TableColumn[] = [\n    { title: 'Nome da API', field: 'nomeApi' },\n    { title: 'Swagger da API', field: 'swaggerLink' },\n  ];\n\n\n    const data = application?.apis?.map(api => {\n      return {\n        nomeApi: `${api.name}`,\n        swaggerLink: <Link style={{ color: 'blue' }} to={`${urlBackstage}/api/proxy/apim/discovery/swagger/api/${api.name}`}>Link do Swagger</Link>,\n      };\n    });\n\n    return (\n      <Table\n        title=\"Apis Utilizadas\"\n        options={{ search: false, paging: false }}\n        columns={columns}\n        data={data || []}\n      />\n    );\n  };\n\n  const ApimConsumidasFetchComponent = (urlBackstage: string, applicationId: string) => {\n    const appAux = useAsync(async (): Promise<Application> => {\n      const response = await fetch(`${urlBackstage}/api/proxy/apim/applications/${applicationId}`);\n      return response.json();\n    });\n\n    const apiIdsAux = useAsync(async (): Promise<APIId[]> => {\n      const response = await fetch(`${urlBackstage}/api/proxy/apim/applications/${applicationId}/apis/`);\n      return response.json();\n    });\n\n    const apisAux = useAsync(async (): Promise<Api[]> => {\n      const response = await fetch(`${urlBackstage}/api/proxy/apim/discovery/apis/`);\n      return response.json();\n    });\n\n    if (appAux.loading || apiIdsAux.loading || apisAux.loading) {\n      return <Progress />;\n    } else if (appAux.error) {\n      return <ResponseErrorPanel error={appAux.error} />;\n    } else if (apiIdsAux.error) {\n      return <ResponseErrorPanel error={apiIdsAux.error} />;\n    } else if (apisAux.error) {\n      return <ResponseErrorPanel error={apisAux.error} />;\n    }\n\n    const ids = apiIdsAux.value?.map(apis => apis.apiId) || [];\n\n    const apis = apisAux.value?.filter(api => ids?.includes(api.id));\n\n    const application: Application = {\n      apis: apis,\n      name: appAux.value?.name,\n      state: appAux.value?.state\n    };\n\n    return <DenseTable application={application} urlBackstage={urlBackstage} />;\n  };\n\n  export { DenseTable, ApimConsumidasFetchComponent };\n"], "names": ["apis"], "mappings": ";;;;AA0BA,MAAM,UAAa,GAAA,CAAC,EAAE,WAAA,EAAa,cAAoC,KAAA;AAErE,EAAA,MAAM,OAAyB,GAAA;AAAA,IAC7B,EAAE,KAAA,EAAO,aAAe,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACzC,EAAE,KAAA,EAAO,gBAAkB,EAAA,KAAA,EAAO,aAAc;AAAA,GAClD;AAGE,EAAA,MAAM,IAAO,GAAA,WAAA,EAAa,IAAM,EAAA,GAAA,CAAI,CAAO,GAAA,KAAA;AACzC,IAAO,OAAA;AAAA,MACL,OAAA,EAAS,CAAG,EAAA,GAAA,CAAI,IAAI,CAAA,CAAA;AAAA,MACpB,WAAa,kBAAA,KAAA,CAAA,aAAA,CAAC,IAAK,EAAA,EAAA,KAAA,EAAO,EAAE,KAAO,EAAA,MAAA,EAAU,EAAA,EAAA,EAAI,GAAG,YAAY,CAAA,sCAAA,EAAyC,GAAI,CAAA,IAAI,MAAI,iBAAe;AAAA,KACtI;AAAA,GACD,CAAA;AAED,EACE,uBAAA,KAAA,CAAA,aAAA;AAAA,IAAC,KAAA;AAAA,IAAA;AAAA,MACC,KAAM,EAAA,iBAAA;AAAA,MACN,OAAS,EAAA,EAAE,MAAQ,EAAA,KAAA,EAAO,QAAQ,KAAM,EAAA;AAAA,MACxC,OAAA;AAAA,MACA,IAAA,EAAM,QAAQ;AAAC;AAAA,GACjB;AAEJ;AAEM,MAAA,4BAAA,GAA+B,CAAC,YAAA,EAAsB,aAA0B,KAAA;AACpF,EAAM,MAAA,MAAA,GAAS,SAAS,YAAkC;AACxD,IAAA,MAAM,WAAW,MAAM,KAAA,CAAM,GAAG,YAAY,CAAA,6BAAA,EAAgC,aAAa,CAAE,CAAA,CAAA;AAC3F,IAAA,OAAO,SAAS,IAAK,EAAA;AAAA,GACtB,CAAA;AAED,EAAM,MAAA,SAAA,GAAY,SAAS,YAA8B;AACvD,IAAA,MAAM,WAAW,MAAM,KAAA,CAAM,GAAG,YAAY,CAAA,6BAAA,EAAgC,aAAa,CAAQ,MAAA,CAAA,CAAA;AACjG,IAAA,OAAO,SAAS,IAAK,EAAA;AAAA,GACtB,CAAA;AAED,EAAM,MAAA,OAAA,GAAU,SAAS,YAA4B;AACnD,IAAA,MAAM,QAAW,GAAA,MAAM,KAAM,CAAA,CAAA,EAAG,YAAY,CAAiC,+BAAA,CAAA,CAAA;AAC7E,IAAA,OAAO,SAAS,IAAK,EAAA;AAAA,GACtB,CAAA;AAED,EAAA,IAAI,MAAO,CAAA,OAAA,IAAW,SAAU,CAAA,OAAA,IAAW,QAAQ,OAAS,EAAA;AAC1D,IAAA,2CAAQ,QAAS,EAAA,IAAA,CAAA;AAAA,GACnB,MAAA,IAAW,OAAO,KAAO,EAAA;AACvB,IAAA,uBAAQ,KAAA,CAAA,aAAA,CAAA,kBAAA,EAAA,EAAmB,KAAO,EAAA,MAAA,CAAO,KAAO,EAAA,CAAA;AAAA,GAClD,MAAA,IAAW,UAAU,KAAO,EAAA;AAC1B,IAAA,uBAAQ,KAAA,CAAA,aAAA,CAAA,kBAAA,EAAA,EAAmB,KAAO,EAAA,SAAA,CAAU,KAAO,EAAA,CAAA;AAAA,GACrD,MAAA,IAAW,QAAQ,KAAO,EAAA;AACxB,IAAA,uBAAQ,KAAA,CAAA,aAAA,CAAA,kBAAA,EAAA,EAAmB,KAAO,EAAA,OAAA,CAAQ,KAAO,EAAA,CAAA;AAAA;AAGnD,EAAM,MAAA,GAAA,GAAM,UAAU,KAAO,EAAA,GAAA,CAAI,CAAAA,KAAQA,KAAAA,KAAAA,CAAK,KAAK,CAAA,IAAK,EAAC;AAEzD,EAAM,MAAA,IAAA,GAAO,QAAQ,KAAO,EAAA,MAAA,CAAO,SAAO,GAAK,EAAA,QAAA,CAAS,GAAI,CAAA,EAAE,CAAC,CAAA;AAE/D,EAAA,MAAM,WAA2B,GAAA;AAAA,IAC/B,IAAA;AAAA,IACA,IAAA,EAAM,OAAO,KAAO,EAAA,IAAA;AAAA,IACpB,KAAA,EAAO,OAAO,KAAO,EAAA;AAAA,GACvB;AAEA,EAAO,uBAAA,KAAA,CAAA,aAAA,CAAC,UAAW,EAAA,EAAA,WAAA,EAA0B,YAA4B,EAAA,CAAA;AAC3E;;;;"}
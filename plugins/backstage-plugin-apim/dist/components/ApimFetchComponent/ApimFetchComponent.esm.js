import React from 'react';
import { Progress, ResponseErrorPanel, Link, Table } from '@backstage/core-components';
import useAsync from 'react-use/lib/useAsync';

const DenseTable = ({ application, urlBackstage }) => {
  const columns = [
    { title: "Nome da API", field: "nomeApi" },
    { title: "Swagger da API", field: "swaggerLink" }
  ];
  const data = application?.apis?.map((api) => {
    return {
      nomeApi: `${api.name}`,
      swaggerLink: /* @__PURE__ */ React.createElement(Link, { style: { color: "blue" }, to: `${urlBackstage}/api/proxy/apim/discovery/swagger/api/${api.name}` }, "Link do Swagger")
    };
  });
  return /* @__PURE__ */ React.createElement(
    Table,
    {
      title: "Apis Utilizadas",
      options: { search: false, paging: false },
      columns,
      data: data || []
    }
  );
};
const ApimConsumidasFetchComponent = (urlBackstage, applicationId) => {
  const appAux = useAsync(async () => {
    const response = await fetch(`${urlBackstage}/api/proxy/apim/applications/${applicationId}`);
    return response.json();
  });
  const apiIdsAux = useAsync(async () => {
    const response = await fetch(`${urlBackstage}/api/proxy/apim/applications/${applicationId}/apis/`);
    return response.json();
  });
  const apisAux = useAsync(async () => {
    const response = await fetch(`${urlBackstage}/api/proxy/apim/discovery/apis/`);
    return response.json();
  });
  if (appAux.loading || apiIdsAux.loading || apisAux.loading) {
    return /* @__PURE__ */ React.createElement(Progress, null);
  } else if (appAux.error) {
    return /* @__PURE__ */ React.createElement(ResponseErrorPanel, { error: appAux.error });
  } else if (apiIdsAux.error) {
    return /* @__PURE__ */ React.createElement(ResponseErrorPanel, { error: apiIdsAux.error });
  } else if (apisAux.error) {
    return /* @__PURE__ */ React.createElement(ResponseErrorPanel, { error: apisAux.error });
  }
  const ids = apiIdsAux.value?.map((apis2) => apis2.apiId) || [];
  const apis = apisAux.value?.filter((api) => ids?.includes(api.id));
  const application = {
    apis,
    name: appAux.value?.name,
    state: appAux.value?.state
  };
  return /* @__PURE__ */ React.createElement(DenseTable, { application, urlBackstage });
};

export { ApimConsumidasFetchComponent, DenseTable };
//# sourceMappingURL=ApimFetchComponent.esm.js.map

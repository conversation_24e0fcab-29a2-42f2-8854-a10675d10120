{"include": ["src", "config.d.ts", "dev", "migrations"], "exclude": ["node_modules"], "compilerOptions": {"outDir": "dist-types", "rootDir": ".", "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "jsx": "react", "module": "esnext", "moduleResolution": "node", "target": "es2019", "lib": ["DOM", "DOM.Iterable", "ESNext"]}}
import React from 'react';
import { Link, Progress, ResponseErrorPanel, Table, TableColumn } from '@backstage/core-components';
import useAsync from 'react-use/lib/useAsync';

type DenseTableProps = {
  application: Application | undefined,
  urlBackstage: string | undefined
};

type Application = {
  name: string | undefined,
  state: string | undefined,
  apis: Api[] | undefined
};

type APIId = {
  apiId: string,
  enabled: boolean
};

type Api = {
  name: string,
  id: string,
  uri: string
};

const DenseTable = ({ application, urlBackstage }: DenseTableProps) => {

  const columns: TableColumn[] = [
    { title: 'Nome da API', field: 'nomeApi' },
    { title: 'Swagger da API', field: 'swaggerLink' },
  ];


    const data = application?.apis?.map(api => {
      return {
        nomeApi: `${api.name}`,
        swaggerLink: <Link style={{ color: 'blue' }} to={`${urlBackstage}/api/proxy/apim/discovery/swagger/api/${api.name}`}>Link do Swagger</Link>,
      };
    });

    return (
      <Table
        title="Apis Utilizadas"
        options={{ search: false, paging: false }}
        columns={columns}
        data={data || []}
      />
    );
  };

  const ApimConsumidasFetchComponent = (urlBackstage: string, applicationId: string) => {
    const appAux = useAsync(async (): Promise<Application> => {
      const response = await fetch(`${urlBackstage}/api/proxy/apim/applications/${applicationId}`);
      return response.json();
    });

    const apiIdsAux = useAsync(async (): Promise<APIId[]> => {
      const response = await fetch(`${urlBackstage}/api/proxy/apim/applications/${applicationId}/apis/`);
      return response.json();
    });

    const apisAux = useAsync(async (): Promise<Api[]> => {
      const response = await fetch(`${urlBackstage}/api/proxy/apim/discovery/apis/`);
      return response.json();
    });

    if (appAux.loading || apiIdsAux.loading || apisAux.loading) {
      return <Progress />;
    } else if (appAux.error) {
      return <ResponseErrorPanel error={appAux.error} />;
    } else if (apiIdsAux.error) {
      return <ResponseErrorPanel error={apiIdsAux.error} />;
    } else if (apisAux.error) {
      return <ResponseErrorPanel error={apisAux.error} />;
    }

    const ids = apiIdsAux.value?.map(apis => apis.apiId) || [];

    const apis = apisAux.value?.filter(api => ids?.includes(api.id));

    const application: Application = {
      apis: apis,
      name: appAux.value?.name,
      state: appAux.value?.state
    };

    return <DenseTable application={application} urlBackstage={urlBackstage} />;
  };

  export { DenseTable, ApimConsumidasFetchComponent };

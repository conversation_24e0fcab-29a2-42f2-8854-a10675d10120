import React from 'react';
import { Typography, Grid } from '@material-ui/core';
import {
  InfoCard,
  Content,
  ContentHeader,
  SupportButton,
  EmptyState,
} from '@backstage/core-components';
import { ApimConsumidasFetchComponent } from '../ApimFetchComponent';
import { configApiRef, useApi } from '@backstage/core-plugin-api';
import { useEntity } from "@backstage/plugin-catalog-react"


export const ApimComponent = () => {

  const { entity } = useEntity();
  const config = useApi(configApiRef);

  const urlBackstage = config.getConfig('backend').getString('baseUrl') || "";
  const codeApplication: string = entity.metadata.annotations?.['engie.apim/code-application'] || "";


  if (codeApplication) {
    return (
      <Content>
        <ContentHeader title="API Management">
          <SupportButton>Centralização das APIs disponibilizadas e Utilizadas pela Aplicação.</SupportButton>
        </ContentHeader>
        <Grid container spacing={3} direction="column">
          <Grid item>
            <InfoCard title="Minhas APIs">
              <Typography variant="body1">
                Centralização das APIs disponibilizadas e Utilizadas pela Aplicação.
              </Typography>
            </InfoCard>
          </Grid>
          <Grid item>
            {ApimConsumidasFetchComponent(urlBackstage, codeApplication)}
          </Grid>
        </Grid>
      </Content>
    )
  } else {
    return (
      <EmptyState
        missing="info"
        title="Sem Informações para essa Aplicação"
        description={`Revise o catalogo-info da aplicação adicionando as tags: engie.apim/code-application`} />
    );
  }

};
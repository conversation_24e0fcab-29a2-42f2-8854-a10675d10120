import React from 'react';
type DenseTableProps = {
    application: Application | undefined;
    urlBackstage: string | undefined;
};
type Application = {
    name: string | undefined;
    state: string | undefined;
    apis: Api[] | undefined;
};
type Api = {
    name: string;
    id: string;
    uri: string;
};
declare const DenseTable: ({ application, urlBackstage }: DenseTableProps) => React.JSX.Element;
declare const ApimConsumidasFetchComponent: (urlBackstage: string, applicationId: string) => React.JSX.Element;
export { DenseTable, ApimConsumidasFetchComponent };
//# sourceMappingURL=ApimFetchComponent.d.ts.map
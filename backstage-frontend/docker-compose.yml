version: "3.8"

services:
  backstage-frontend:
    image: backstage-frontend:latest
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BITBUCKET_SERVER_USERNAME: ${BITBUCKET_SERVER_USERNAME}
        BITBUCKET_SERVER_PASSWORD: ${BITBUCKET_SERVER_PASSWORD}
    env_file:
      - .env
    container_name: backstage-frontend
    ports:
      - "3000:80"
    restart: unless-stopped
    networks:
      - backstage-network

networks:
  backstage-network:
    driver: bridge

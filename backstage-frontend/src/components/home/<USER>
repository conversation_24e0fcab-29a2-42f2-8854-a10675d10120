import {
  HomePageToolkit,
  TemplateBackstageLogoIcon,
} from '@backstage/plugin-home';
import { wrapInTestApp, TestApiProvider } from '@backstage/test-utils';
import { Content, Page, InfoCard } from '@backstage/core-components';
import {
  entityRouteRef,
} from '@backstage/plugin-catalog-react';
import { configApiRef } from '@backstage/core-plugin-api';
import { ConfigReader } from '@backstage/config';
import { HomePageSearchBar, searchPlugin } from '@backstage/plugin-search';
import {
  searchApiRef,
  SearchContextProvider,
} from '@backstage/plugin-search-react';
import { Grid, makeStyles } from '@material-ui/core';
import React, { ComponentType, PropsWithChildren } from 'react';
import EngieLogoFull from '../Root/logo/engie.png';


export default {
  title: 'Plugins/Home/Templates',
  decorators: [
    (Story: ComponentType<PropsWithChildren<{}>>) =>
      wrapInTestApp(
        <>
          <TestApiProvider
            apis={[
              [searchApiRef, { query: () => Promise.resolve({ results: [] }) }],
              [
                configApiRef,
                new ConfigReader({
                  stackoverflow: {
                    baseUrl: 'https://api.stackexchange.com/2.2',
                  },
                }),
              ],
            ]}
          >
            <Story />
          </TestApiProvider>
        </>,
        {
          mountedRoutes: {
            '/hello-company': searchPlugin.routes.root,
            '/catalog/:namespace/:kind/:name': entityRouteRef,
          },
        },
      ),
  ],
};

const useStyles = makeStyles(theme => ({
  searchBarInput: {
    maxWidth: '60vw',
    margin: 'auto',
    backgroundColor: theme.palette.background.paper,
    borderRadius: '50px',
    boxShadow: theme.shadows[1],
  },
  searchBarOutline: {
    borderStyle: 'none'
  }
}));

export const HomePage = () => {
  const classes = useStyles();

  return (
    <SearchContextProvider>
      <Page themeId="home">
        <Content>
          <Grid container justifyContent="center" spacing={6}>
            <img src={EngieLogoFull} width='auto' height='60' />
            <Grid container item xs={12} justifyContent='center'>
              <HomePageSearchBar
                InputProps={{ classes: { root: classes.searchBarInput, notchedOutline: classes.searchBarOutline } }}
                placeholder="Search"
              />
            </Grid>
            <Grid container item xs={12}>
              <Grid item xs={12} md={6}>
                <InfoCard title="Portal Self Service - Engie">
                  {/* placeholder for content */}
                  Portal de Novas aplicações da Engie
                  <div style={{ height: 370 }} />
                </InfoCard>
              </Grid>
              <Grid item xs={12} md={6}>
                <HomePageToolkit title='Nossas Aplicações DevSecOps'
                  tools={aplicacoes}
                />
              </Grid>
            </Grid>
          </Grid>
        </Content>
      </Page>
    </SearchContextProvider>
  );
};

const aplicacoes = new Array(
  {
    url: 'https://engiebrasilenergia.atlassian.net/wiki/spaces/DEV/overview?homepageId=54394885',
    label: 'DevSecOps',
    icon: <TemplateBackstageLogoIcon />,
  },
  {
    url: 'https://engiebrasilenergia.atlassian.net/wiki/home',
    label: 'Confluence',
    icon: <TemplateBackstageLogoIcon />,
  },
  {
    url: 'http://almappro.tractebelenergia.com.br/bitbucket',
    label: 'Bitbucket',
    icon: <TemplateBackstageLogoIcon />,
  },
  {
    url: 'http://almappro.tractebelenergia.com.br/bamboo',
    label: 'Bamboo',
    icon: <TemplateBackstageLogoIcon />,
  },
  {
    url: 'https://jira.engie.com.br',
    label: 'Jira',
    icon: <TemplateBackstageLogoIcon />,
  },
  {
    url: 'https://console-openshift-console.apps.ocp.ds55.local/dashboards',
    label: 'Openshift 4',
    icon: <TemplateBackstageLogoIcon />,
  },
  {
    url: 'https://kibana-openshift-logging.apps.ocp.ds55.local/',
    label: 'Kibana Openshift 4',
    icon: <TemplateBackstageLogoIcon />,
  },
  {
    url: 'https://grafana-openshift-monitoring.apps.ocp.ds55.local/',
    label: 'Grafana Openshift 4',
    icon: <TemplateBackstageLogoIcon />,
  },
  {
    url: 'http://br-ebe-grafana.ds55.local:3000/',
    label: 'Grafana Geral',
    icon: <TemplateBackstageLogoIcon />,
  },
  {
    url: 'http://engieacessos.ds55.local/idm',
    label: 'Solicitações de Acesso',
    icon: <TemplateBackstageLogoIcon />,
  }
)
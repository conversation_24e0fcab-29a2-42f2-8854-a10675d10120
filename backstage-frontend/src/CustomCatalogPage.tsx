import {
    PageWithHeader,
    Content,
    ContentHeader,
    SupportButton,
  } from '@backstage/core-components';
  import { useApi, configApiRef } from '@backstage/core-plugin-api';
  import { CatalogTable } from '@backstage/plugin-catalog';
  import {
    EntityListProvider,
    CatalogFilterLayout,
    EntityKindPicker,
    EntityLifecyclePicker,
    EntityOwnerPicker,
    EntityProcessingStatusPicker,
    EntityTagPicker,
    EntityTypePicker,
    UserListPicker,
  } from '@backstage/plugin-catalog-react';
  import React from 'react';
  
  export const CustomCatalogPage = () => {
    const orgName =
      useApi(configApiRef).getOptionalString('organization.name') ?? 'Backstage';
  
    return (
      <PageWithHeader title={orgName} pageTitleOverride='EngieDevops' themeId="home">
        <Content>
          <ContentHeader title="Aplicações da Engie Brasil Energia" >
            <SupportButton>Páginas de Suporte à Ferramenta</SupportButton>
          </ContentHeader>
          <EntityListProvider pagination>
            <CatalogFilterLayout>
              <CatalogFilterLayout.Filters>
                <EntityKindPicker allowedKinds={['Component', 'Template']} />
                <EntityTypePicker initialFilter='application' />
                <UserListPicker initialFilter='all' />
                <EntityOwnerPicker />
                <EntityLifecyclePicker />
                <EntityTagPicker />
                <EntityProcessingStatusPicker />
              </CatalogFilterLayout.Filters>
              <CatalogFilterLayout.Content>
                <CatalogTable />
              </CatalogFilterLayout.Content>
            </CatalogFilterLayout>
          </EntityListProvider>
        </Content>
      </PageWithHeader>
    );
  };
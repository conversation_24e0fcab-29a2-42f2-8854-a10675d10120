#### STAGE 1 - BUILD
FROM node:20-bookworm-slim as builder

ARG BITBUCKET_SERVER_USERNAME
ARG BITBUCKET_SERVER_PASSWORD

ENV BITBUCKET_SERVER_USERNAME=${BITBUCKET_SERVER_USERNAME}
ENV BITBUCKET_SERVER_PASSWORD=${BITBUCKET_SERVER_PASSWORD}

WORKDIR /app

RUN apt-get update && apt-get install -y git && \
    git config --global url."http://${BITBUCKET_SERVER_USERNAME}:${BITBUCKET_SERVER_PASSWORD}@almappro.tractebelenergia.com.br/".insteadOf "http://almappro.tractebelenergia.com.br/" && \
    git config --global http.sslVerify false

COPY package.json yarn.lock ./

RUN yarn

COPY . .

RUN yarn build

#### STAGE 2 - NGINX
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html

COPY src/infra/nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]

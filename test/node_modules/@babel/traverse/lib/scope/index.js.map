{"version": 3, "names": ["_renamer", "require", "_index", "_binding", "_globals", "_t", "t", "_cache", "assignmentExpression", "callExpression", "cloneNode", "getBindingIdentifiers", "identifier", "isArrayExpression", "isBinary", "isCallExpression", "isClass", "isClassBody", "isClassDeclaration", "isExportAllDeclaration", "isExportDefaultDeclaration", "isExportNamedDeclaration", "isFunctionDeclaration", "isIdentifier", "isImportDeclaration", "isLiteral", "isMemberExpression", "isMethod", "isModuleSpecifier", "is<PERSON>ull<PERSON>iteral", "isObjectExpression", "isProperty", "isPureish", "isRegExpLiteral", "is<PERSON><PERSON><PERSON>", "isTaggedTemplateExpression", "isTemplateLiteral", "isThisExpression", "isUnaryExpression", "isVariableDeclaration", "expressionStatement", "matchesPattern", "memberExpression", "numericLiteral", "toIdentifier", "variableDeclaration", "variableDeclarator", "isRecordExpression", "isTupleExpression", "isObjectProperty", "isTopicReference", "isMetaProperty", "isPrivateName", "isExportDeclaration", "buildUndefinedNode", "sequenceExpression", "gatherNodeParts", "node", "parts", "type", "_node$specifiers", "source", "specifiers", "length", "e", "declaration", "local", "push", "value", "object", "property", "name", "callee", "properties", "argument", "key", "left", "id", "expression", "meta", "openingElement", "openingFragment", "namespace", "resetScope", "scope", "references", "Object", "create", "bindings", "globals", "uids", "NOT_LOCAL_BINDING", "Symbol", "for", "collectorVisitor", "ForStatement", "path", "declar", "get", "isVar", "parentScope", "getFunctionParent", "getProgramParent", "registerBinding", "Declaration", "isBlockScoped", "parent", "registerDeclaration", "ImportDeclaration", "getBlockParent", "TSImportEqualsDeclaration", "ReferencedIdentifier", "state", "isTSQualifiedName", "right", "parentPath", "isTSImportEqualsDeclaration", "ForXStatement", "isPattern", "constantViolations", "ExportDeclaration", "exit", "binding", "getBinding", "reference", "decl", "declarations", "keys", "LabeledStatement", "AssignmentExpression", "assignments", "UpdateExpression", "UnaryExpression", "operator", "BlockScoped", "CatchClause", "Function", "params", "param", "isFunctionExpression", "ClassExpression", "TSTypeAnnotation", "skip", "scopeVisitor", "uid", "<PERSON><PERSON>", "constructor", "block", "inited", "labels", "data", "crawling", "cached", "scopeCache", "set", "Map", "_parent", "_path", "shouldSkip", "<PERSON><PERSON><PERSON>", "isScope", "generateDeclaredUidIdentifier", "generateUidIdentifier", "generateUid", "replace", "i", "<PERSON><PERSON><PERSON><PERSON>", "hasBinding", "hasGlobal", "hasReference", "program", "generateUidBasedOnNode", "defaultName", "join", "slice", "generateUidIdentifierBasedOnNode", "isStatic", "constant", "maybeGenerateMemoised", "dont<PERSON><PERSON>", "checkBlockScopedCollisions", "kind", "duplicate", "hub", "buildError", "TypeError", "rename", "old<PERSON>ame", "newName", "renamer", "Renamer", "arguments", "dump", "sep", "repeat", "console", "log", "violations", "get<PERSON><PERSON><PERSON>", "registerLabel", "label", "isLabeledStatement", "declare", "isTypeDeclaration", "importKind", "specifier", "isTypeSpecifier", "isImportSpecifier", "registerConstantViolation", "ids", "getAssignmentIdentifiers", "_this$getBinding", "reassign", "bindingPath", "ReferenceError", "declarators", "getOuterBindingIdentifiers", "getOwnBinding", "Binding", "addGlobal", "hasUid", "isPure", "constantsOnly", "_node$decorators", "superClass", "decorators", "body", "method", "elem", "elements", "prop", "_node$decorators2", "computed", "_node$decorators3", "static", "expressions", "tag", "noGlobals", "quasi", "isStringLiteral", "setData", "val", "getData", "removeData", "init", "crawl", "isProgram", "programParent", "traverse", "visitors", "merge", "visit", "enter", "call", "typeVisitors", "ref", "opts", "getPatternParent", "isBlockStatement", "isSwitchStatement", "unique", "isFunction", "pushContainer", "isLoop", "isCatchClause", "ensureBlock", "blockHoist", "_blockHoist", "dataKey", "declar<PERSON><PERSON>", "unshiftContainer", "declarator", "len", "Error", "isFunctionParent", "isBlockParent", "getAllBindings", "bindingIdentifierEquals", "getBindingIdentifier", "previousPath", "_previousPath", "isArrowFunctionExpression", "_this$getBinding2", "getOwnBindingIdentifier", "hasOwnBinding", "noUids", "upToScope", "includes", "contextVariables", "parentHasBinding", "_this$parent", "moveBindingTo", "info", "removeOwnBinding", "removeBinding", "_this$getBinding3", "hoistVariables", "emit", "seen", "Set", "isVariableDeclarator", "has", "add", "firstId", "isFor", "replaceWith", "remove", "expr", "isForStatement", "exports", "default", "builtin", "prototype", "_renameFromMap", "map", "_generateUid", "toArray", "arrayLikeIsIterable", "isGenericType", "helper<PERSON><PERSON>", "args", "unshift", "addHelper", "getAllBindingsOfKind", "kinds", "defineProperties", "parentBlock", "configurable", "enumerable"], "sources": ["../../src/scope/index.ts"], "sourcesContent": ["import Renamer from \"./lib/renamer.ts\";\nimport type NodePath from \"../path/index.ts\";\nimport traverse from \"../index.ts\";\nimport Binding from \"./binding.ts\";\nimport type { BindingKind } from \"./binding.ts\";\nimport globals from \"globals\";\nimport {\n  assignmentExpression,\n  callExpression,\n  cloneNode,\n  getBindingIdentifiers,\n  identifier,\n  isArrayExpression,\n  isBinary,\n  isCallExpression,\n  isClass,\n  isClassBody,\n  isClassDeclaration,\n  isExportAllDeclaration,\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n  isFunctionDeclaration,\n  isIdentifier,\n  isImportDeclaration,\n  isLiteral,\n  isMemberExpression,\n  isMethod,\n  isModuleSpecifier,\n  isNullLiteral,\n  isObjectExpression,\n  isProperty,\n  isPureish,\n  isRegExpLiteral,\n  isSuper,\n  isTaggedTemplateExpression,\n  isTemplateLiteral,\n  isThisExpression,\n  isUnaryExpression,\n  isVariableDeclaration,\n  expressionStatement,\n  matchesPattern,\n  memberExpression,\n  numericLiteral,\n  toIdentifier,\n  variableDeclaration,\n  variableDeclarator,\n  isRecordExpression,\n  isTupleExpression,\n  isObjectProperty,\n  isTopicReference,\n  isMetaProperty,\n  isPrivateName,\n  isExportDeclaration,\n  buildUndefinedNode,\n  sequenceExpression,\n} from \"@babel/types\";\nimport * as t from \"@babel/types\";\nimport { scope as scopeCache } from \"../cache.ts\";\nimport type { ExplodedVisitor, Visitor } from \"../types.ts\";\n\ntype NodePart = string | number | boolean;\n// Recursively gathers the identifying names of a node.\nfunction gatherNodeParts(node: t.Node, parts: NodePart[]) {\n  switch (node?.type) {\n    default:\n      if (isImportDeclaration(node) || isExportDeclaration(node)) {\n        if (\n          (isExportAllDeclaration(node) ||\n            isExportNamedDeclaration(node) ||\n            isImportDeclaration(node)) &&\n          node.source\n        ) {\n          gatherNodeParts(node.source, parts);\n        } else if (\n          (isExportNamedDeclaration(node) || isImportDeclaration(node)) &&\n          node.specifiers?.length\n        ) {\n          for (const e of node.specifiers) gatherNodeParts(e, parts);\n        } else if (\n          (isExportDefaultDeclaration(node) ||\n            isExportNamedDeclaration(node)) &&\n          node.declaration\n        ) {\n          gatherNodeParts(node.declaration, parts);\n        }\n      } else if (isModuleSpecifier(node)) {\n        // todo(flow->ts): should condition instead be:\n        //    ```\n        //    t.isExportSpecifier(node) ||\n        //    t.isImportDefaultSpecifier(node) ||\n        //    t.isImportNamespaceSpecifier(node) ||\n        //    t.isImportSpecifier(node)\n        //    ```\n        //    allowing only nodes with `.local`?\n        // @ts-expect-error todo(flow->ts)\n        gatherNodeParts(node.local, parts);\n      } else if (\n        isLiteral(node) &&\n        !isNullLiteral(node) &&\n        !isRegExpLiteral(node) &&\n        !isTemplateLiteral(node)\n      ) {\n        parts.push(node.value);\n      }\n      break;\n\n    case \"MemberExpression\":\n    case \"OptionalMemberExpression\":\n    case \"JSXMemberExpression\":\n      gatherNodeParts(node.object, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"Identifier\":\n    case \"JSXIdentifier\":\n      parts.push(node.name);\n      break;\n\n    case \"CallExpression\":\n    case \"OptionalCallExpression\":\n    case \"NewExpression\":\n      gatherNodeParts(node.callee, parts);\n      break;\n\n    case \"ObjectExpression\":\n    case \"ObjectPattern\":\n      for (const e of node.properties) {\n        gatherNodeParts(e, parts);\n      }\n      break;\n\n    case \"SpreadElement\":\n    case \"RestElement\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"ObjectProperty\":\n    case \"ObjectMethod\":\n    case \"ClassProperty\":\n    case \"ClassMethod\":\n    case \"ClassPrivateProperty\":\n    case \"ClassPrivateMethod\":\n      gatherNodeParts(node.key, parts);\n      break;\n\n    case \"ThisExpression\":\n      parts.push(\"this\");\n      break;\n\n    case \"Super\":\n      parts.push(\"super\");\n      break;\n\n    case \"Import\":\n      parts.push(\"import\");\n      break;\n\n    case \"DoExpression\":\n      parts.push(\"do\");\n      break;\n\n    case \"YieldExpression\":\n      parts.push(\"yield\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AwaitExpression\":\n      parts.push(\"await\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AssignmentExpression\":\n      gatherNodeParts(node.left, parts);\n      break;\n\n    case \"VariableDeclarator\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"FunctionExpression\":\n    case \"FunctionDeclaration\":\n    case \"ClassExpression\":\n    case \"ClassDeclaration\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"PrivateName\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"ParenthesizedExpression\":\n      gatherNodeParts(node.expression, parts);\n      break;\n\n    case \"UnaryExpression\":\n    case \"UpdateExpression\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"MetaProperty\":\n      gatherNodeParts(node.meta, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"JSXElement\":\n      gatherNodeParts(node.openingElement, parts);\n      break;\n\n    case \"JSXOpeningElement\":\n      gatherNodeParts(node.name, parts);\n      break;\n\n    case \"JSXFragment\":\n      gatherNodeParts(node.openingFragment, parts);\n      break;\n\n    case \"JSXOpeningFragment\":\n      parts.push(\"Fragment\");\n      break;\n\n    case \"JSXNamespacedName\":\n      gatherNodeParts(node.namespace, parts);\n      gatherNodeParts(node.name, parts);\n      break;\n  }\n}\n\nfunction resetScope(scope: Scope) {\n  scope.references = Object.create(null);\n  scope.bindings = Object.create(null);\n  scope.globals = Object.create(null);\n  scope.uids = Object.create(null);\n}\n\ninterface CollectVisitorState {\n  assignments: NodePath<t.AssignmentExpression>[];\n  references: NodePath<t.Identifier | t.JSXIdentifier>[];\n  constantViolations: NodePath[];\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var NOT_LOCAL_BINDING = Symbol.for(\n    \"should not be considered a local binding\",\n  );\n}\n\nconst collectorVisitor: Visitor<CollectVisitorState> = {\n  ForStatement(path) {\n    const declar = path.get(\"init\");\n    // delegate block scope handling to the `BlockScoped` method\n    if (declar.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", declar);\n    }\n  },\n\n  Declaration(path) {\n    // delegate block scope handling to the `BlockScoped` method\n    if (path.isBlockScoped()) return;\n\n    // delegate import handing to the `ImportDeclaration` method\n    if (path.isImportDeclaration()) return;\n\n    // this will be hit again once we traverse into it after this iteration\n    if (path.isExportDeclaration()) return;\n\n    // we've ran into a declaration!\n    const parent =\n      path.scope.getFunctionParent() || path.scope.getProgramParent();\n    parent.registerDeclaration(path);\n  },\n\n  ImportDeclaration(path) {\n    // import may only appear in the top level or inside a module/namespace (for TS/flow)\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  TSImportEqualsDeclaration(path) {\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  ReferencedIdentifier(path, state) {\n    if (t.isTSQualifiedName(path.parent) && path.parent.right === path.node) {\n      return;\n    }\n    if (path.parentPath.isTSImportEqualsDeclaration()) return;\n    state.references.push(path);\n  },\n\n  ForXStatement(path, state) {\n    const left = path.get(\"left\");\n    if (left.isPattern() || left.isIdentifier()) {\n      state.constantViolations.push(path);\n    }\n    // delegate block scope handling to the `BlockScoped` method\n    else if (left.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", left);\n    }\n  },\n\n  ExportDeclaration: {\n    exit(path) {\n      const { node, scope } = path;\n      // ExportAllDeclaration does not have `declaration`\n      if (isExportAllDeclaration(node)) return;\n      const declar = node.declaration;\n      if (isClassDeclaration(declar) || isFunctionDeclaration(declar)) {\n        const id = declar.id;\n        if (!id) return;\n\n        const binding = scope.getBinding(id.name);\n        binding?.reference(path);\n      } else if (isVariableDeclaration(declar)) {\n        for (const decl of declar.declarations) {\n          for (const name of Object.keys(getBindingIdentifiers(decl))) {\n            const binding = scope.getBinding(name);\n            binding?.reference(path);\n          }\n        }\n      }\n    },\n  },\n\n  LabeledStatement(path) {\n    path.scope.getBlockParent().registerDeclaration(path);\n  },\n\n  AssignmentExpression(path, state) {\n    state.assignments.push(path);\n  },\n\n  UpdateExpression(path, state) {\n    state.constantViolations.push(path);\n  },\n\n  UnaryExpression(path, state) {\n    if (path.node.operator === \"delete\") {\n      state.constantViolations.push(path);\n    }\n  },\n\n  BlockScoped(path) {\n    let scope = path.scope;\n    if (scope.path === path) scope = scope.parent;\n\n    const parent = scope.getBlockParent();\n    parent.registerDeclaration(path);\n\n    // Register class identifier in class' scope if this is a class declaration.\n    if (path.isClassDeclaration() && path.node.id) {\n      const id = path.node.id;\n      const name = id.name;\n\n      path.scope.bindings[name] = path.scope.parent.getBinding(name);\n    }\n  },\n\n  CatchClause(path) {\n    path.scope.registerBinding(\"let\", path);\n  },\n\n  Function(path) {\n    const params: Array<NodePath> = path.get(\"params\");\n    for (const param of params) {\n      path.scope.registerBinding(\"param\", param);\n    }\n\n    // Register function expression id after params. When the id\n    // collides with a function param, the id effectively can't be\n    // referenced: here we registered it as a constantViolation\n    if (\n      path.isFunctionExpression() &&\n      path.node.id &&\n      (process.env.BABEL_8_BREAKING ||\n        // @ts-expect-error Fixme: document symbol ast properties\n        !path.node.id[NOT_LOCAL_BINDING])\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  ClassExpression(path) {\n    if (\n      path.node.id &&\n      (process.env.BABEL_8_BREAKING ||\n        // @ts-expect-error Fixme: document symbol ast properties\n        !path.node.id[NOT_LOCAL_BINDING])\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  TSTypeAnnotation(path) {\n    path.skip();\n  },\n};\n\nlet scopeVisitor: ExplodedVisitor<CollectVisitorState>;\n\nlet uid = 0;\n\nexport type { Binding };\n\nexport { Scope as default };\nclass Scope {\n  uid;\n\n  path: NodePath;\n  block: t.Pattern | t.Scopable;\n\n  inited;\n\n  labels: Map<string, NodePath<t.LabeledStatement>>;\n  bindings: { [name: string]: Binding };\n  references: { [name: string]: true };\n  globals: { [name: string]: t.Identifier | t.JSXIdentifier };\n  uids: { [name: string]: boolean };\n  data: { [key: string | symbol]: unknown };\n  crawling: boolean;\n\n  /**\n   * This searches the current \"scope\" and collects all references/bindings\n   * within.\n   */\n  constructor(path: NodePath<t.Pattern | t.Scopable>) {\n    const { node } = path;\n    const cached = scopeCache.get(node);\n    // Sometimes, a scopable path is placed higher in the AST tree.\n    // In these cases, have to create a new Scope.\n    if (cached?.path === path) {\n      return cached;\n    }\n    scopeCache.set(node, this);\n\n    this.uid = uid++;\n\n    this.block = node;\n    this.path = path;\n\n    this.labels = new Map();\n    this.inited = false;\n  }\n\n  /**\n   * Globals.\n   */\n\n  static globals = Object.keys(globals.builtin);\n\n  /**\n   * Variables available in current context.\n   */\n\n  static contextVariables = [\"arguments\", \"undefined\", \"Infinity\", \"NaN\"];\n\n  get parent() {\n    let parent,\n      path = this.path;\n    do {\n      // Skip method scope if coming from inside computed key or decorator expression\n      const shouldSkip = path.key === \"key\" || path.listKey === \"decorators\";\n      path = path.parentPath;\n      if (shouldSkip && path.isMethod()) path = path.parentPath;\n      if (path?.isScope()) parent = path;\n    } while (path && !parent);\n\n    return parent?.scope;\n  }\n\n  /**\n   * Generate a unique identifier and add it to the current scope.\n   */\n\n  generateDeclaredUidIdentifier(name?: string) {\n    const id = this.generateUidIdentifier(name);\n    this.push({ id });\n    return cloneNode(id);\n  }\n\n  /**\n   * Generate a unique identifier.\n   */\n\n  generateUidIdentifier(name?: string) {\n    return identifier(this.generateUid(name));\n  }\n\n  /**\n   * Generate a unique `_id1` binding.\n   */\n\n  generateUid(name: string = \"temp\"): string {\n    name = toIdentifier(name).replace(/^_+/, \"\").replace(/\\d+$/g, \"\");\n\n    let uid;\n    let i = 0;\n    do {\n      uid = `_${name}`;\n\n      // Ideally we would just use (i - 1) as the suffix, but that generates\n      // unnecessary changes in every single file generated by Babel :)\n      //\n      // i:       0        1  2  3  4  5  6  7  8  9 10 11 12 13 14 ...\n      // suffix:  (empty)  2  3  4  5  6  7  8  9  0  1 10 11 12 13 ...\n      if (i >= 11) uid += i - 1;\n      else if (i >= 9) uid += i - 9;\n      else if (i >= 1) uid += i + 1;\n      i++;\n    } while (\n      this.hasLabel(uid) ||\n      this.hasBinding(uid) ||\n      this.hasGlobal(uid) ||\n      this.hasReference(uid)\n    );\n\n    const program = this.getProgramParent();\n    program.references[uid] = true;\n    program.uids[uid] = true;\n\n    return uid;\n  }\n\n  generateUidBasedOnNode(node: t.Node, defaultName?: string) {\n    const parts: NodePart[] = [];\n    gatherNodeParts(node, parts);\n\n    let id = parts.join(\"$\");\n    id = id.replace(/^_/, \"\") || defaultName || \"ref\";\n\n    return this.generateUid(id.slice(0, 20));\n  }\n\n  /**\n   * Generate a unique identifier based on a node.\n   */\n\n  generateUidIdentifierBasedOnNode(node: t.Node, defaultName?: string) {\n    return identifier(this.generateUidBasedOnNode(node, defaultName));\n  }\n\n  /**\n   * Determine whether evaluating the specific input `node` is a consequenceless reference. ie.\n   * evaluating it won't result in potentially arbitrary code from being ran. The following are\n   * allowed and determined not to cause side effects:\n   *\n   *  - `this` expressions\n   *  - `super` expressions\n   *  - Bound identifiers\n   */\n\n  isStatic(node: t.Node): boolean {\n    if (isThisExpression(node) || isSuper(node) || isTopicReference(node)) {\n      return true;\n    }\n\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding) {\n        return binding.constant;\n      } else {\n        return this.hasBinding(node.name);\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Possibly generate a memoised identifier if it is not static and has consequences.\n   */\n\n  maybeGenerateMemoised(node: t.Node, dontPush?: boolean) {\n    if (this.isStatic(node)) {\n      return null;\n    } else {\n      const id = this.generateUidIdentifierBasedOnNode(node);\n      if (!dontPush) {\n        this.push({ id });\n        return cloneNode(id);\n      }\n      return id;\n    }\n  }\n\n  checkBlockScopedCollisions(\n    local: Binding,\n    kind: BindingKind,\n    name: string,\n    id: any,\n  ) {\n    // ignore parameters\n    if (kind === \"param\") return;\n\n    // Ignore existing binding if it's the name of the current function or\n    // class expression\n    if (local.kind === \"local\") return;\n\n    const duplicate =\n      // don't allow duplicate bindings to exist alongside\n      kind === \"let\" ||\n      local.kind === \"let\" ||\n      local.kind === \"const\" ||\n      local.kind === \"module\" ||\n      // don't allow a local of param with a kind of let\n      (local.kind === \"param\" && kind === \"const\");\n\n    if (duplicate) {\n      throw this.path.hub.buildError(\n        id,\n        `Duplicate declaration \"${name}\"`,\n        TypeError,\n      );\n    }\n  }\n\n  rename(\n    oldName: string,\n    newName?: string,\n    // prettier-ignore\n    /* Babel 7 - block?: t.Pattern | t.Scopable */\n  ) {\n    const binding = this.getBinding(oldName);\n    if (binding) {\n      newName ||= this.generateUidIdentifier(oldName).name;\n      const renamer = new Renamer(binding, oldName, newName);\n      if (process.env.BABEL_8_BREAKING) {\n        renamer.rename();\n      } else {\n        // @ts-ignore(Babel 7 vs Babel 8) TODO: Delete this\n        renamer.rename(arguments[2]);\n      }\n    }\n  }\n\n  dump() {\n    const sep = \"-\".repeat(60);\n    console.log(sep);\n    let scope: Scope = this;\n    do {\n      console.log(\"#\", scope.block.type);\n      for (const name of Object.keys(scope.bindings)) {\n        const binding = scope.bindings[name];\n        console.log(\" -\", name, {\n          constant: binding.constant,\n          references: binding.references,\n          violations: binding.constantViolations.length,\n          kind: binding.kind,\n        });\n      }\n    } while ((scope = scope.parent));\n    console.log(sep);\n  }\n\n  hasLabel(name: string) {\n    return !!this.getLabel(name);\n  }\n\n  getLabel(name: string) {\n    return this.labels.get(name);\n  }\n\n  registerLabel(path: NodePath<t.LabeledStatement>) {\n    this.labels.set(path.node.label.name, path);\n  }\n\n  registerDeclaration(path: NodePath) {\n    if (path.isLabeledStatement()) {\n      this.registerLabel(path);\n    } else if (path.isFunctionDeclaration()) {\n      this.registerBinding(\"hoisted\", path.get(\"id\"), path);\n    } else if (path.isVariableDeclaration()) {\n      const declarations = path.get(\"declarations\");\n      const { kind } = path.node;\n      for (const declar of declarations) {\n        this.registerBinding(\n          kind === \"using\" || kind === \"await using\" ? \"const\" : kind,\n          declar,\n        );\n      }\n    } else if (path.isClassDeclaration()) {\n      if (path.node.declare) return;\n      this.registerBinding(\"let\", path);\n    } else if (path.isImportDeclaration()) {\n      const isTypeDeclaration =\n        path.node.importKind === \"type\" || path.node.importKind === \"typeof\";\n      const specifiers = path.get(\"specifiers\");\n      for (const specifier of specifiers) {\n        const isTypeSpecifier =\n          isTypeDeclaration ||\n          (specifier.isImportSpecifier() &&\n            (specifier.node.importKind === \"type\" ||\n              specifier.node.importKind === \"typeof\"));\n\n        this.registerBinding(isTypeSpecifier ? \"unknown\" : \"module\", specifier);\n      }\n    } else if (path.isExportDeclaration()) {\n      // todo: improve babel-types\n      const declar = path.get(\"declaration\") as NodePath;\n      if (\n        declar.isClassDeclaration() ||\n        declar.isFunctionDeclaration() ||\n        declar.isVariableDeclaration()\n      ) {\n        this.registerDeclaration(declar);\n      }\n    } else {\n      this.registerBinding(\"unknown\", path);\n    }\n  }\n\n  buildUndefinedNode() {\n    return buildUndefinedNode();\n  }\n\n  registerConstantViolation(path: NodePath) {\n    const ids = path.getAssignmentIdentifiers();\n    for (const name of Object.keys(ids)) {\n      this.getBinding(name)?.reassign(path);\n    }\n  }\n\n  registerBinding(\n    kind: Binding[\"kind\"],\n    path: NodePath,\n    bindingPath: NodePath = path,\n  ) {\n    if (!kind) throw new ReferenceError(\"no `kind`\");\n\n    if (path.isVariableDeclaration()) {\n      const declarators: Array<NodePath> = path.get(\"declarations\");\n      for (const declar of declarators) {\n        this.registerBinding(kind, declar);\n      }\n      return;\n    }\n\n    const parent = this.getProgramParent();\n    const ids = path.getOuterBindingIdentifiers(true);\n\n    for (const name of Object.keys(ids)) {\n      parent.references[name] = true;\n\n      for (const id of ids[name]) {\n        const local = this.getOwnBinding(name);\n\n        if (local) {\n          // same identifier so continue safely as we're likely trying to register it\n          // multiple times\n          if (local.identifier === id) continue;\n\n          this.checkBlockScopedCollisions(local, kind, name, id);\n        }\n\n        // A redeclaration of an existing variable is a modification\n        if (local) {\n          local.reassign(bindingPath);\n        } else {\n          this.bindings[name] = new Binding({\n            identifier: id,\n            scope: this,\n            path: bindingPath,\n            kind: kind,\n          });\n        }\n      }\n    }\n  }\n\n  addGlobal(node: t.Identifier | t.JSXIdentifier) {\n    this.globals[node.name] = node;\n  }\n\n  hasUid(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.uids[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasGlobal(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.globals[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasReference(name: string): boolean {\n    return !!this.getProgramParent().references[name];\n  }\n\n  isPure(node: t.Node, constantsOnly?: boolean): boolean {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (!binding) return false;\n      if (constantsOnly) return binding.constant;\n      return true;\n    } else if (\n      isThisExpression(node) ||\n      isMetaProperty(node) ||\n      isTopicReference(node) ||\n      isPrivateName(node)\n    ) {\n      return true;\n    } else if (isClass(node)) {\n      if (node.superClass && !this.isPure(node.superClass, constantsOnly)) {\n        return false;\n      }\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return this.isPure(node.body, constantsOnly);\n    } else if (isClassBody(node)) {\n      for (const method of node.body) {\n        if (!this.isPure(method, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isBinary(node)) {\n      return (\n        this.isPure(node.left, constantsOnly) &&\n        this.isPure(node.right, constantsOnly)\n      );\n    } else if (isArrayExpression(node) || isTupleExpression(node)) {\n      for (const elem of node.elements) {\n        if (elem !== null && !this.isPure(elem, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isObjectExpression(node) || isRecordExpression(node)) {\n      for (const prop of node.properties) {\n        if (!this.isPure(prop, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isMethod(node)) {\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return true;\n    } else if (isProperty(node)) {\n      // @ts-expect-error todo(flow->ts): computed in not present on private properties\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      if (isObjectProperty(node) || node.static) {\n        if (node.value !== null && !this.isPure(node.value, constantsOnly)) {\n          return false;\n        }\n      }\n      return true;\n    } else if (isUnaryExpression(node)) {\n      return this.isPure(node.argument, constantsOnly);\n    } else if (isTemplateLiteral(node)) {\n      for (const expression of node.expressions) {\n        if (!this.isPure(expression, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isTaggedTemplateExpression(node)) {\n      return (\n        matchesPattern(node.tag, \"String.raw\") &&\n        !this.hasBinding(\"String\", { noGlobals: true }) &&\n        this.isPure(node.quasi, constantsOnly)\n      );\n    } else if (isMemberExpression(node)) {\n      return (\n        !node.computed &&\n        isIdentifier(node.object) &&\n        node.object.name === \"Symbol\" &&\n        isIdentifier(node.property) &&\n        node.property.name !== \"for\" &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true })\n      );\n    } else if (isCallExpression(node)) {\n      return (\n        matchesPattern(node.callee, \"Symbol.for\") &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true }) &&\n        node.arguments.length === 1 &&\n        t.isStringLiteral(node.arguments[0])\n      );\n    } else {\n      return isPureish(node);\n    }\n  }\n\n  /**\n   * Set some arbitrary data on the current scope.\n   */\n\n  setData(key: string | symbol, val: any) {\n    return (this.data[key] = val);\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key`.\n   */\n\n  getData(key: string | symbol): any {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) return data;\n    } while ((scope = scope.parent));\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key` and if it exists,\n   * remove it.\n   */\n\n  removeData(key: string) {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) scope.data[key] = null;\n    } while ((scope = scope.parent));\n  }\n\n  init() {\n    if (!this.inited) {\n      this.inited = true;\n      this.crawl();\n    }\n  }\n\n  crawl() {\n    const path = this.path;\n\n    resetScope(this);\n    this.data = Object.create(null);\n\n    let scope: Scope = this;\n    do {\n      if (scope.crawling) return;\n      if (scope.path.isProgram()) {\n        break;\n      }\n    } while ((scope = scope.parent));\n\n    const programParent = scope;\n\n    const state: CollectVisitorState = {\n      references: [],\n      constantViolations: [],\n      assignments: [],\n    };\n\n    this.crawling = true;\n    scopeVisitor ||= traverse.visitors.merge([\n      {\n        Scope(path) {\n          resetScope(path.scope);\n        },\n      },\n      collectorVisitor,\n    ]);\n    // traverse does not visit the root node, here we explicitly collect\n    // root node binding info when the root is not a Program.\n    if (path.type !== \"Program\") {\n      for (const visit of scopeVisitor.enter) {\n        visit.call(state, path, state);\n      }\n      const typeVisitors = scopeVisitor[path.type];\n      if (typeVisitors) {\n        for (const visit of typeVisitors.enter) {\n          visit.call(state, path, state);\n        }\n      }\n    }\n    path.traverse(scopeVisitor, state);\n    this.crawling = false;\n\n    // register assignments\n    for (const path of state.assignments) {\n      // register undeclared bindings as globals\n      const ids = path.getAssignmentIdentifiers();\n      for (const name of Object.keys(ids)) {\n        if (path.scope.getBinding(name)) continue;\n        programParent.addGlobal(ids[name]);\n      }\n\n      // register as constant violation\n      path.scope.registerConstantViolation(path);\n    }\n\n    // register references\n    for (const ref of state.references) {\n      const binding = ref.scope.getBinding(ref.node.name);\n      if (binding) {\n        binding.reference(ref);\n      } else {\n        programParent.addGlobal(ref.node);\n      }\n    }\n\n    // register constant violations\n    for (const path of state.constantViolations) {\n      path.scope.registerConstantViolation(path);\n    }\n  }\n\n  push(opts: {\n    id: t.ArrayPattern | t.Identifier | t.ObjectPattern;\n    init?: t.Expression;\n    unique?: boolean;\n    _blockHoist?: number | undefined;\n    kind?: \"var\" | \"let\" | \"const\";\n  }) {\n    let path = this.path;\n\n    if (path.isPattern()) {\n      path = this.getPatternParent().path;\n    } else if (!path.isBlockStatement() && !path.isProgram()) {\n      path = this.getBlockParent().path;\n    }\n\n    if (path.isSwitchStatement()) {\n      path = (this.getFunctionParent() || this.getProgramParent()).path;\n    }\n\n    const { init, unique, kind = \"var\", id } = opts;\n\n    // When injecting a non-const non-initialized binding inside\n    // an IIFE, if the number of call arguments is less than or\n    // equal to the number of function parameters, we can safely\n    // inject the binding into the parameter list.\n    if (\n      !init &&\n      !unique &&\n      (kind === \"var\" || kind === \"let\") &&\n      path.isFunction() &&\n      // @ts-expect-error ArrowFunctionExpression never has a name\n      !path.node.name &&\n      isCallExpression(path.parent, { callee: path.node }) &&\n      path.parent.arguments.length <= path.node.params.length &&\n      isIdentifier(id)\n    ) {\n      path.pushContainer(\"params\", id);\n      path.scope.registerBinding(\n        \"param\",\n        path.get(\"params\")[path.node.params.length - 1],\n      );\n      return;\n    }\n\n    if (path.isLoop() || path.isCatchClause() || path.isFunction()) {\n      path.ensureBlock();\n      path = path.get(\"body\");\n    }\n\n    const blockHoist = opts._blockHoist == null ? 2 : opts._blockHoist;\n\n    const dataKey = `declaration:${kind}:${blockHoist}`;\n    let declarPath = !unique && path.getData(dataKey);\n\n    if (!declarPath) {\n      const declar = variableDeclaration(kind, []);\n      // @ts-expect-error todo(flow->ts): avoid modifying nodes\n      declar._blockHoist = blockHoist;\n\n      [declarPath] = (path as NodePath<t.BlockStatement>).unshiftContainer(\n        \"body\",\n        [declar],\n      );\n      if (!unique) path.setData(dataKey, declarPath);\n    }\n\n    const declarator = variableDeclarator(id, init);\n    const len = declarPath.node.declarations.push(declarator);\n    path.scope.registerBinding(kind, declarPath.get(\"declarations\")[len - 1]);\n  }\n\n  /**\n   * Walk up to the top of the scope tree and get the `Program`.\n   */\n\n  getProgramParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isProgram()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\"Couldn't find a Program\");\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a Function or return null.\n   */\n\n  getFunctionParent(): Scope | null {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isFunctionParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    return null;\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a BlockStatement/Loop/Program/Function/Switch or reach the\n   * very top and hit Program.\n   */\n\n  getBlockParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isBlockParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walk up from a pattern scope (function param initializer) until we hit a non-pattern scope,\n   * then returns its block parent\n   * @returns An ancestry scope whose path is a block parent\n   */\n  getPatternParent() {\n    let scope: Scope = this;\n    do {\n      if (!scope.path.isPattern()) {\n        return scope.getBlockParent();\n      }\n    } while ((scope = scope.parent.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walks the scope tree and gathers **all** bindings.\n   */\n\n  getAllBindings(): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    let scope: Scope = this;\n    do {\n      for (const key of Object.keys(scope.bindings)) {\n        if (key in ids === false) {\n          ids[key] = scope.bindings[key];\n        }\n      }\n      scope = scope.parent;\n    } while (scope);\n\n    return ids;\n  }\n\n  bindingIdentifierEquals(name: string, node: t.Node): boolean {\n    return this.getBindingIdentifier(name) === node;\n  }\n\n  getBinding(name: string): Binding | undefined {\n    let scope: Scope = this;\n    let previousPath;\n\n    do {\n      const binding = scope.getOwnBinding(name);\n      if (binding) {\n        // Check if a pattern is a part of parameter expressions.\n        // Note: for performance reason we skip checking previousPath.parentPath.isFunction()\n        // because `scope.path` is validated as scope in packages/babel-types/src/validators/isScope.js\n        // That is, if a scope path is pattern, its parent must be Function/CatchClause\n\n        // Spec *********: The closure created by this expression should not have visibility of\n        // declarations in the function body. If the binding is not a `param`-kind (as function parameters)\n        // or `local`-kind (as id in function expression),\n        // then it must be defined inside the function body, thus it should be skipped\n        if (\n          previousPath?.isPattern() &&\n          binding.kind !== \"param\" &&\n          binding.kind !== \"local\"\n        ) {\n          // do nothing\n        } else {\n          return binding;\n        }\n      } else if (\n        !binding &&\n        name === \"arguments\" &&\n        scope.path.isFunction() &&\n        !scope.path.isArrowFunctionExpression()\n      ) {\n        break;\n      }\n      previousPath = scope.path;\n    } while ((scope = scope.parent));\n  }\n\n  getOwnBinding(name: string): Binding | undefined {\n    return this.bindings[name];\n  }\n\n  // todo: return probably can be undefined…\n  getBindingIdentifier(name: string): t.Identifier {\n    return this.getBinding(name)?.identifier;\n  }\n\n  // todo: flow->ts return probably can be undefined\n  getOwnBindingIdentifier(name: string): t.Identifier {\n    const binding = this.bindings[name];\n    return binding?.identifier;\n  }\n\n  hasOwnBinding(name: string) {\n    return !!this.getOwnBinding(name);\n  }\n\n  // By default, we consider generated UIDs as bindings.\n  // This is because they are almost always used to declare variables,\n  // and since the scope isn't always up-to-date it's better to assume that\n  // there is a variable with that name. The `noUids` option can be used to\n  // turn off this behavior, for example if you know that the generate UID\n  // was used to declare a variable in a different scope.\n  hasBinding(\n    name: string,\n    opts?:\n      | boolean\n      | { noGlobals?: boolean; noUids?: boolean; upToScope?: Scope },\n  ) {\n    if (!name) return false;\n    // TODO: Only accept the object form.\n    let noGlobals;\n    let noUids;\n    let upToScope;\n    if (typeof opts === \"object\") {\n      noGlobals = opts.noGlobals;\n      noUids = opts.noUids;\n      upToScope = opts.upToScope;\n    } else if (typeof opts === \"boolean\") {\n      noGlobals = opts;\n    }\n    let scope: Scope = this;\n    do {\n      if (upToScope === scope) {\n        break;\n      }\n      if (scope.hasOwnBinding(name)) {\n        return true;\n      }\n    } while ((scope = scope.parent));\n\n    if (!noUids && this.hasUid(name)) return true;\n    if (!noGlobals && Scope.globals.includes(name)) return true;\n    if (!noGlobals && Scope.contextVariables.includes(name)) return true;\n    return false;\n  }\n\n  parentHasBinding(\n    name: string,\n    opts?: { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    return this.parent?.hasBinding(name, opts);\n  }\n\n  /**\n   * Move a binding of `name` to another `scope`.\n   */\n\n  moveBindingTo(name: string, scope: Scope) {\n    const info = this.getBinding(name);\n    if (info) {\n      info.scope.removeOwnBinding(name);\n      info.scope = scope;\n      scope.bindings[name] = info;\n    }\n  }\n\n  removeOwnBinding(name: string) {\n    delete this.bindings[name];\n  }\n\n  removeBinding(name: string) {\n    // clear literal binding\n    this.getBinding(name)?.scope.removeOwnBinding(name);\n\n    // clear uids with this name - https://github.com/babel/babel/issues/2101\n    let scope: Scope = this;\n    do {\n      if (scope.uids[name]) {\n        scope.uids[name] = false;\n      }\n    } while ((scope = scope.parent));\n  }\n\n  /**\n   * Hoist all the `var` variable to the beginning of the function/program\n   * scope where their binding will be actually defined. For exmaple,\n   *     { var x = 2 }\n   * will be transformed to\n   *     var x; { x = 2 }\n   *\n   * @param emit A custom function to emit `var` declarations, for example to\n   *   emit them in a different scope.\n   */\n  hoistVariables(\n    emit: (id: t.Identifier, hasInit: boolean) => void = id =>\n      this.push({ id }),\n  ) {\n    this.crawl();\n\n    const seen = new Set();\n    for (const name of Object.keys(this.bindings)) {\n      const binding = this.bindings[name];\n      if (!binding) continue;\n      const { path } = binding;\n      if (!path.isVariableDeclarator()) continue;\n      const { parent, parentPath } = path;\n\n      if (parent.kind !== \"var\" || seen.has(parent)) continue;\n      seen.add(path.parent);\n\n      let firstId;\n      const init = [];\n      for (const decl of parent.declarations) {\n        firstId ??= decl.id;\n        if (decl.init) {\n          init.push(assignmentExpression(\"=\", decl.id, decl.init));\n        }\n\n        const ids = Object.keys(getBindingIdentifiers(decl, false, true, true));\n        for (const name of ids) {\n          emit(identifier(name), decl.init != null);\n        }\n      }\n\n      // for (var i in test)\n      if (parentPath.parentPath.isFor({ left: parent })) {\n        parentPath.replaceWith(firstId);\n      } else if (init.length === 0) {\n        parentPath.remove();\n      } else {\n        const expr = init.length === 1 ? init[0] : sequenceExpression(init);\n        if (parentPath.parentPath.isForStatement({ init: parent })) {\n          parentPath.replaceWith(expr);\n        } else {\n          parentPath.replaceWith(expressionStatement(expr));\n        }\n      }\n    }\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /** @deprecated Not used in our codebase */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype._renameFromMap = function _renameFromMap(\n    map: Record<string | symbol, unknown>,\n    oldName: string | symbol,\n    newName: string | symbol,\n    value: unknown,\n  ) {\n    if (map[oldName]) {\n      map[newName] = value;\n      map[oldName] = null;\n    }\n  };\n\n  /**\n   * Traverse node with current scope and path.\n   *\n   * !!! WARNING !!!\n   * This method assumes that `this.path` is the NodePath representing `node`.\n   * After running the traversal, the `.parentPath` of the NodePaths\n   * corresponding to `node`'s children will be set to `this.path`.\n   *\n   * There is no good reason to use this method, since the only safe way to use\n   * it is equivalent to `scope.path.traverse(opts, state)`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.traverse = function <S>(\n    this: Scope,\n    node: any,\n    opts: any,\n    state?: S,\n  ) {\n    traverse(node, opts, this, state, this.path);\n  };\n\n  /**\n   * Generate an `_id1`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype._generateUid = function _generateUid(\n    name: string,\n    i: number,\n  ) {\n    let id = name;\n    if (i > 1) id += i;\n    return `_${id}`;\n  };\n\n  // TODO: (Babel 8) Split i in two parameters, and use an object of flags\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.toArray = function toArray(\n    this: Scope,\n    node: t.Node,\n    i?: number | boolean,\n    arrayLikeIsIterable?: boolean | void,\n  ) {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding?.constant && binding.path.isGenericType(\"Array\")) {\n        return node;\n      }\n    }\n\n    if (isArrayExpression(node)) {\n      return node;\n    }\n\n    if (isIdentifier(node, { name: \"arguments\" })) {\n      return callExpression(\n        memberExpression(\n          memberExpression(\n            memberExpression(identifier(\"Array\"), identifier(\"prototype\")),\n            identifier(\"slice\"),\n          ),\n          identifier(\"call\"),\n        ),\n        [node],\n      );\n    }\n\n    let helperName;\n    const args = [node];\n    if (i === true) {\n      // Used in array-spread to create an array.\n      helperName = \"toConsumableArray\";\n    } else if (typeof i === \"number\") {\n      args.push(numericLiteral(i));\n\n      // Used in array-rest to create an array from a subset of an iterable.\n      helperName = \"slicedToArray\";\n      // TODO if (this.hub.isLoose(\"es6.forOf\")) helperName += \"-loose\";\n    } else {\n      // Used in array-rest to create an array\n      helperName = \"toArray\";\n    }\n\n    if (arrayLikeIsIterable) {\n      args.unshift(this.path.hub.addHelper(helperName));\n      helperName = \"maybeArrayLike\";\n    }\n\n    // @ts-expect-error todo(flow->ts): t.Node is not valid to use in args, function argument typeneeds to be clarified\n    return callExpression(this.path.hub.addHelper(helperName), args);\n  };\n\n  /**\n   * Walks the scope tree and gathers all declarations of `kind`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.getAllBindingsOfKind = function getAllBindingsOfKind(\n    ...kinds: string[]\n  ): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    for (const kind of kinds) {\n      let scope: Scope = this;\n      do {\n        for (const name of Object.keys(scope.bindings)) {\n          const binding = scope.bindings[name];\n          if (binding.kind === kind) ids[name] = binding;\n        }\n        scope = scope.parent;\n      } while (scope);\n    }\n\n    return ids;\n  };\n\n  Object.defineProperties(Scope.prototype, {\n    parentBlock: {\n      configurable: true,\n      enumerable: true,\n      get(this: Scope) {\n        return this.path.parent;\n      },\n    },\n    hub: {\n      configurable: true,\n      enumerable: true,\n      get(this: Scope) {\n        return this.path.hub;\n      },\n    },\n  });\n}\n\ntype _Binding = Binding;\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Scope {\n  export type Binding = _Binding;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AAiDsB,IAAAK,CAAA,GAAAD,EAAA;AAEtB,IAAAE,MAAA,GAAAN,OAAA;AAAkD;EAlDhDO,oBAAoB;EACpBC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,UAAU;EACVC,iBAAiB;EACjBC,QAAQ;EACRC,gBAAgB;EAChBC,OAAO;EACPC,WAAW;EACXC,kBAAkB;EAClBC,sBAAsB;EACtBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,qBAAqB;EACrBC,YAAY;EACZC,mBAAmB;EACnBC,SAAS;EACTC,kBAAkB;EAClBC,QAAQ;EACRC,iBAAiB;EACjBC,aAAa;EACbC,kBAAkB;EAClBC,UAAU;EACVC,SAAS;EACTC,eAAe;EACfC,OAAO;EACPC,0BAA0B;EAC1BC,iBAAiB;EACjBC,gBAAgB;EAChBC,iBAAiB;EACjBC,qBAAqB;EACrBC,mBAAmB;EACnBC,cAAc;EACdC,gBAAgB;EAChBC,cAAc;EACdC,YAAY;EACZC,mBAAmB;EACnBC,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC,gBAAgB;EAChBC,gBAAgB;EAChBC,cAAc;EACdC,aAAa;EACbC,mBAAmB;EACnBC,kBAAkB;EAClBC;AAAkB,IAAAlD,EAAA;AAQpB,SAASmD,eAAeA,CAACC,IAAY,EAAEC,KAAiB,EAAE;EACxD,QAAQD,IAAI,oBAAJA,IAAI,CAAEE,IAAI;IAChB;MACE,IAAInC,mBAAmB,CAACiC,IAAI,CAAC,IAAIJ,mBAAmB,CAACI,IAAI,CAAC,EAAE;QAAA,IAAAG,gBAAA;QAC1D,IACE,CAACzC,sBAAsB,CAACsC,IAAI,CAAC,IAC3BpC,wBAAwB,CAACoC,IAAI,CAAC,IAC9BjC,mBAAmB,CAACiC,IAAI,CAAC,KAC3BA,IAAI,CAACI,MAAM,EACX;UACAL,eAAe,CAACC,IAAI,CAACI,MAAM,EAAEH,KAAK,CAAC;QACrC,CAAC,MAAM,IACL,CAACrC,wBAAwB,CAACoC,IAAI,CAAC,IAAIjC,mBAAmB,CAACiC,IAAI,CAAC,MAAAG,gBAAA,GAC5DH,IAAI,CAACK,UAAU,aAAfF,gBAAA,CAAiBG,MAAM,EACvB;UACA,KAAK,MAAMC,CAAC,IAAIP,IAAI,CAACK,UAAU,EAAEN,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;QAC5D,CAAC,MAAM,IACL,CAACtC,0BAA0B,CAACqC,IAAI,CAAC,IAC/BpC,wBAAwB,CAACoC,IAAI,CAAC,KAChCA,IAAI,CAACQ,WAAW,EAChB;UACAT,eAAe,CAACC,IAAI,CAACQ,WAAW,EAAEP,KAAK,CAAC;QAC1C;MACF,CAAC,MAAM,IAAI9B,iBAAiB,CAAC6B,IAAI,CAAC,EAAE;QAUlCD,eAAe,CAACC,IAAI,CAACS,KAAK,EAAER,KAAK,CAAC;MACpC,CAAC,MAAM,IACLjC,SAAS,CAACgC,IAAI,CAAC,IACf,CAAC5B,aAAa,CAAC4B,IAAI,CAAC,IACpB,CAACxB,eAAe,CAACwB,IAAI,CAAC,IACtB,CAACrB,iBAAiB,CAACqB,IAAI,CAAC,EACxB;QACAC,KAAK,CAACS,IAAI,CAACV,IAAI,CAACW,KAAK,CAAC;MACxB;MACA;IAEF,KAAK,kBAAkB;IACvB,KAAK,0BAA0B;IAC/B,KAAK,qBAAqB;MACxBZ,eAAe,CAACC,IAAI,CAACY,MAAM,EAAEX,KAAK,CAAC;MACnCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;IACjB,KAAK,eAAe;MAClBA,KAAK,CAACS,IAAI,CAACV,IAAI,CAACc,IAAI,CAAC;MACrB;IAEF,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,eAAe;MAClBf,eAAe,CAACC,IAAI,CAACe,MAAM,EAAEd,KAAK,CAAC;MACnC;IAEF,KAAK,kBAAkB;IACvB,KAAK,eAAe;MAClB,KAAK,MAAMM,CAAC,IAAIP,IAAI,CAACgB,UAAU,EAAE;QAC/BjB,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;MAC3B;MACA;IAEF,KAAK,eAAe;IACpB,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,gBAAgB;IACrB,KAAK,cAAc;IACnB,KAAK,eAAe;IACpB,KAAK,aAAa;IAClB,KAAK,sBAAsB;IAC3B,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACkB,GAAG,EAAEjB,KAAK,CAAC;MAChC;IAEF,KAAK,gBAAgB;MACnBA,KAAK,CAACS,IAAI,CAAC,MAAM,CAAC;MAClB;IAEF,KAAK,OAAO;MACVT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnB;IAEF,KAAK,QAAQ;MACXT,KAAK,CAACS,IAAI,CAAC,QAAQ,CAAC;MACpB;IAEF,KAAK,cAAc;MACjBT,KAAK,CAACS,IAAI,CAAC,IAAI,CAAC;MAChB;IAEF,KAAK,iBAAiB;MACpBT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,iBAAiB;MACpBA,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,sBAAsB;MACzBF,eAAe,CAACC,IAAI,CAACmB,IAAI,EAAElB,KAAK,CAAC;MACjC;IAEF,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,oBAAoB;IACzB,KAAK,qBAAqB;IAC1B,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,yBAAyB;MAC5BF,eAAe,CAACC,IAAI,CAACqB,UAAU,EAAEpB,KAAK,CAAC;MACvC;IAEF,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,cAAc;MACjBF,eAAe,CAACC,IAAI,CAACsB,IAAI,EAAErB,KAAK,CAAC;MACjCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;MACfF,eAAe,CAACC,IAAI,CAACuB,cAAc,EAAEtB,KAAK,CAAC;MAC3C;IAEF,KAAK,mBAAmB;MACtBF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACwB,eAAe,EAAEvB,KAAK,CAAC;MAC5C;IAEF,KAAK,oBAAoB;MACvBA,KAAK,CAACS,IAAI,CAAC,UAAU,CAAC;MACtB;IAEF,KAAK,mBAAmB;MACtBX,eAAe,CAACC,IAAI,CAACyB,SAAS,EAAExB,KAAK,CAAC;MACtCF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;EACJ;AACF;AAEA,SAASyB,UAAUA,CAACC,KAAY,EAAE;EAChCA,KAAK,CAACC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACtCH,KAAK,CAACI,QAAQ,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACpCH,KAAK,CAACK,OAAO,GAAGH,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACnCH,KAAK,CAACM,IAAI,GAAGJ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAClC;AAQmC;EAEjC,IAAII,iBAAiB,GAAGC,MAAM,CAACC,GAAG,CAChC,0CACF,CAAC;AACH;AAEA,MAAMC,gBAA8C,GAAG;EACrDC,YAAYA,CAACC,IAAI,EAAE;IACjB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAE/B,IAAID,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE;MAClB,MAAM;QAAEf;MAAM,CAAC,GAAGY,IAAI;MACtB,MAAMI,WAAW,GAAGhB,KAAK,CAACiB,iBAAiB,CAAC,CAAC,IAAIjB,KAAK,CAACkB,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEN,MAAM,CAAC;IAC5C;EACF,CAAC;EAEDO,WAAWA,CAACR,IAAI,EAAE;IAEhB,IAAIA,IAAI,CAACS,aAAa,CAAC,CAAC,EAAE;IAG1B,IAAIT,IAAI,CAACxE,mBAAmB,CAAC,CAAC,EAAE;IAGhC,IAAIwE,IAAI,CAAC3C,mBAAmB,CAAC,CAAC,EAAE;IAGhC,MAAMqD,MAAM,GACVV,IAAI,CAACZ,KAAK,CAACiB,iBAAiB,CAAC,CAAC,IAAIL,IAAI,CAACZ,KAAK,CAACkB,gBAAgB,CAAC,CAAC;IACjEI,MAAM,CAACC,mBAAmB,CAACX,IAAI,CAAC;EAClC,CAAC;EAEDY,iBAAiBA,CAACZ,IAAI,EAAE;IAEtB,MAAMU,MAAM,GAAGV,IAAI,CAACZ,KAAK,CAACyB,cAAc,CAAC,CAAC;IAE1CH,MAAM,CAACC,mBAAmB,CAACX,IAAI,CAAC;EAClC,CAAC;EAEDc,yBAAyBA,CAACd,IAAI,EAAE;IAC9B,MAAMU,MAAM,GAAGV,IAAI,CAACZ,KAAK,CAACyB,cAAc,CAAC,CAAC;IAE1CH,MAAM,CAACC,mBAAmB,CAACX,IAAI,CAAC;EAClC,CAAC;EAEDe,oBAAoBA,CAACf,IAAI,EAAEgB,KAAK,EAAE;IAChC,IAAI1G,CAAC,CAAC2G,iBAAiB,CAACjB,IAAI,CAACU,MAAM,CAAC,IAAIV,IAAI,CAACU,MAAM,CAACQ,KAAK,KAAKlB,IAAI,CAACvC,IAAI,EAAE;MACvE;IACF;IACA,IAAIuC,IAAI,CAACmB,UAAU,CAACC,2BAA2B,CAAC,CAAC,EAAE;IACnDJ,KAAK,CAAC3B,UAAU,CAAClB,IAAI,CAAC6B,IAAI,CAAC;EAC7B,CAAC;EAEDqB,aAAaA,CAACrB,IAAI,EAAEgB,KAAK,EAAE;IACzB,MAAMpC,IAAI,GAAGoB,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAItB,IAAI,CAAC0C,SAAS,CAAC,CAAC,IAAI1C,IAAI,CAACrD,YAAY,CAAC,CAAC,EAAE;MAC3CyF,KAAK,CAACO,kBAAkB,CAACpD,IAAI,CAAC6B,IAAI,CAAC;IACrC,CAAC,MAEI,IAAIpB,IAAI,CAACuB,KAAK,CAAC,CAAC,EAAE;MACrB,MAAM;QAAEf;MAAM,CAAC,GAAGY,IAAI;MACtB,MAAMI,WAAW,GAAGhB,KAAK,CAACiB,iBAAiB,CAAC,CAAC,IAAIjB,KAAK,CAACkB,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAE3B,IAAI,CAAC;IAC1C;EACF,CAAC;EAED4C,iBAAiB,EAAE;IACjBC,IAAIA,CAACzB,IAAI,EAAE;MACT,MAAM;QAAEvC,IAAI;QAAE2B;MAAM,CAAC,GAAGY,IAAI;MAE5B,IAAI7E,sBAAsB,CAACsC,IAAI,CAAC,EAAE;MAClC,MAAMwC,MAAM,GAAGxC,IAAI,CAACQ,WAAW;MAC/B,IAAI/C,kBAAkB,CAAC+E,MAAM,CAAC,IAAI3E,qBAAqB,CAAC2E,MAAM,CAAC,EAAE;QAC/D,MAAMpB,EAAE,GAAGoB,MAAM,CAACpB,EAAE;QACpB,IAAI,CAACA,EAAE,EAAE;QAET,MAAM6C,OAAO,GAAGtC,KAAK,CAACuC,UAAU,CAAC9C,EAAE,CAACN,IAAI,CAAC;QACzCmD,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAAC5B,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIzD,qBAAqB,CAAC0D,MAAM,CAAC,EAAE;QACxC,KAAK,MAAM4B,IAAI,IAAI5B,MAAM,CAAC6B,YAAY,EAAE;UACtC,KAAK,MAAMvD,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAACpH,qBAAqB,CAACkH,IAAI,CAAC,CAAC,EAAE;YAC3D,MAAMH,OAAO,GAAGtC,KAAK,CAACuC,UAAU,CAACpD,IAAI,CAAC;YACtCmD,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAAC5B,IAAI,CAAC;UAC1B;QACF;MACF;IACF;EACF,CAAC;EAEDgC,gBAAgBA,CAAChC,IAAI,EAAE;IACrBA,IAAI,CAACZ,KAAK,CAACyB,cAAc,CAAC,CAAC,CAACF,mBAAmB,CAACX,IAAI,CAAC;EACvD,CAAC;EAEDiC,oBAAoBA,CAACjC,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACkB,WAAW,CAAC/D,IAAI,CAAC6B,IAAI,CAAC;EAC9B,CAAC;EAEDmC,gBAAgBA,CAACnC,IAAI,EAAEgB,KAAK,EAAE;IAC5BA,KAAK,CAACO,kBAAkB,CAACpD,IAAI,CAAC6B,IAAI,CAAC;EACrC,CAAC;EAEDoC,eAAeA,CAACpC,IAAI,EAAEgB,KAAK,EAAE;IAC3B,IAAIhB,IAAI,CAACvC,IAAI,CAAC4E,QAAQ,KAAK,QAAQ,EAAE;MACnCrB,KAAK,CAACO,kBAAkB,CAACpD,IAAI,CAAC6B,IAAI,CAAC;IACrC;EACF,CAAC;EAEDsC,WAAWA,CAACtC,IAAI,EAAE;IAChB,IAAIZ,KAAK,GAAGY,IAAI,CAACZ,KAAK;IACtB,IAAIA,KAAK,CAACY,IAAI,KAAKA,IAAI,EAAEZ,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAE7C,MAAMA,MAAM,GAAGtB,KAAK,CAACyB,cAAc,CAAC,CAAC;IACrCH,MAAM,CAACC,mBAAmB,CAACX,IAAI,CAAC;IAGhC,IAAIA,IAAI,CAAC9E,kBAAkB,CAAC,CAAC,IAAI8E,IAAI,CAACvC,IAAI,CAACoB,EAAE,EAAE;MAC7C,MAAMA,EAAE,GAAGmB,IAAI,CAACvC,IAAI,CAACoB,EAAE;MACvB,MAAMN,IAAI,GAAGM,EAAE,CAACN,IAAI;MAEpByB,IAAI,CAACZ,KAAK,CAACI,QAAQ,CAACjB,IAAI,CAAC,GAAGyB,IAAI,CAACZ,KAAK,CAACsB,MAAM,CAACiB,UAAU,CAACpD,IAAI,CAAC;IAChE;EACF,CAAC;EAEDgE,WAAWA,CAACvC,IAAI,EAAE;IAChBA,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAAC,KAAK,EAAEP,IAAI,CAAC;EACzC,CAAC;EAEDwC,QAAQA,CAACxC,IAAI,EAAE;IACb,MAAMyC,MAAuB,GAAGzC,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC;IAClD,KAAK,MAAMwC,KAAK,IAAID,MAAM,EAAE;MAC1BzC,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAAC,OAAO,EAAEmC,KAAK,CAAC;IAC5C;IAKA,IACE1C,IAAI,CAAC2C,oBAAoB,CAAC,CAAC,IAC3B3C,IAAI,CAACvC,IAAI,CAACoB,EAAE,IAGV,CAACmB,IAAI,CAACvC,IAAI,CAACoB,EAAE,CAACc,iBAAiB,CAAC,EAClC;MACAK,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAAC,OAAO,EAAEP,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EAED4C,eAAeA,CAAC5C,IAAI,EAAE;IACpB,IACEA,IAAI,CAACvC,IAAI,CAACoB,EAAE,IAGV,CAACmB,IAAI,CAACvC,IAAI,CAACoB,EAAE,CAACc,iBAAiB,CAAC,EAClC;MACAK,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAAC,OAAO,EAAEP,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EAED6C,gBAAgBA,CAAC7C,IAAI,EAAE;IACrBA,IAAI,CAAC8C,IAAI,CAAC,CAAC;EACb;AACF,CAAC;AAED,IAAIC,YAAkD;AAEtD,IAAIC,GAAG,GAAG,CAAC;AAKX,MAAMC,KAAK,CAAC;EAoBVC,WAAWA,CAAClD,IAAsC,EAAE;IAAA,KAnBpDgD,GAAG;IAAA,KAEHhD,IAAI;IAAA,KACJmD,KAAK;IAAA,KAELC,MAAM;IAAA,KAENC,MAAM;IAAA,KACN7D,QAAQ;IAAA,KACRH,UAAU;IAAA,KACVI,OAAO;IAAA,KACPC,IAAI;IAAA,KACJ4D,IAAI;IAAA,KACJC,QAAQ;IAON,MAAM;MAAE9F;IAAK,CAAC,GAAGuC,IAAI;IACrB,MAAMwD,MAAM,GAAGC,YAAU,CAACvD,GAAG,CAACzC,IAAI,CAAC;IAGnC,IAAI,CAAA+F,MAAM,oBAANA,MAAM,CAAExD,IAAI,MAAKA,IAAI,EAAE;MACzB,OAAOwD,MAAM;IACf;IACAC,YAAU,CAACC,GAAG,CAACjG,IAAI,EAAE,IAAI,CAAC;IAE1B,IAAI,CAACuF,GAAG,GAAGA,GAAG,EAAE;IAEhB,IAAI,CAACG,KAAK,GAAG1F,IAAI;IACjB,IAAI,CAACuC,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAACqD,MAAM,GAAG,IAAIM,GAAG,CAAC,CAAC;IACvB,IAAI,CAACP,MAAM,GAAG,KAAK;EACrB;EAcA,IAAI1C,MAAMA,CAAA,EAAG;IAAA,IAAAkD,OAAA;IACX,IAAIlD,MAAM;MACRV,IAAI,GAAG,IAAI,CAACA,IAAI;IAClB,GAAG;MAAA,IAAA6D,KAAA;MAED,MAAMC,UAAU,GAAG9D,IAAI,CAACrB,GAAG,KAAK,KAAK,IAAIqB,IAAI,CAAC+D,OAAO,KAAK,YAAY;MACtE/D,IAAI,GAAGA,IAAI,CAACmB,UAAU;MACtB,IAAI2C,UAAU,IAAI9D,IAAI,CAACrE,QAAQ,CAAC,CAAC,EAAEqE,IAAI,GAAGA,IAAI,CAACmB,UAAU;MACzD,KAAA0C,KAAA,GAAI7D,IAAI,aAAJ6D,KAAA,CAAMG,OAAO,CAAC,CAAC,EAAEtD,MAAM,GAAGV,IAAI;IACpC,CAAC,QAAQA,IAAI,IAAI,CAACU,MAAM;IAExB,QAAAkD,OAAA,GAAOlD,MAAM,qBAANkD,OAAA,CAAQxE,KAAK;EACtB;EAMA6E,6BAA6BA,CAAC1F,IAAa,EAAE;IAC3C,MAAMM,EAAE,GAAG,IAAI,CAACqF,qBAAqB,CAAC3F,IAAI,CAAC;IAC3C,IAAI,CAACJ,IAAI,CAAC;MAAEU;IAAG,CAAC,CAAC;IACjB,OAAOnE,SAAS,CAACmE,EAAE,CAAC;EACtB;EAMAqF,qBAAqBA,CAAC3F,IAAa,EAAE;IACnC,OAAO3D,UAAU,CAAC,IAAI,CAACuJ,WAAW,CAAC5F,IAAI,CAAC,CAAC;EAC3C;EAMA4F,WAAWA,CAAC5F,IAAY,GAAG,MAAM,EAAU;IACzCA,IAAI,GAAG3B,YAAY,CAAC2B,IAAI,CAAC,CAAC6F,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAEjE,IAAIpB,GAAG;IACP,IAAIqB,CAAC,GAAG,CAAC;IACT,GAAG;MACDrB,GAAG,GAAG,IAAIzE,IAAI,EAAE;MAOhB,IAAI8F,CAAC,IAAI,EAAE,EAAErB,GAAG,IAAIqB,CAAC,GAAG,CAAC,CAAC,KACrB,IAAIA,CAAC,IAAI,CAAC,EAAErB,GAAG,IAAIqB,CAAC,GAAG,CAAC,CAAC,KACzB,IAAIA,CAAC,IAAI,CAAC,EAAErB,GAAG,IAAIqB,CAAC,GAAG,CAAC;MAC7BA,CAAC,EAAE;IACL,CAAC,QACC,IAAI,CAACC,QAAQ,CAACtB,GAAG,CAAC,IAClB,IAAI,CAACuB,UAAU,CAACvB,GAAG,CAAC,IACpB,IAAI,CAACwB,SAAS,CAACxB,GAAG,CAAC,IACnB,IAAI,CAACyB,YAAY,CAACzB,GAAG,CAAC;IAGxB,MAAM0B,OAAO,GAAG,IAAI,CAACpE,gBAAgB,CAAC,CAAC;IACvCoE,OAAO,CAACrF,UAAU,CAAC2D,GAAG,CAAC,GAAG,IAAI;IAC9B0B,OAAO,CAAChF,IAAI,CAACsD,GAAG,CAAC,GAAG,IAAI;IAExB,OAAOA,GAAG;EACZ;EAEA2B,sBAAsBA,CAAClH,IAAY,EAAEmH,WAAoB,EAAE;IACzD,MAAMlH,KAAiB,GAAG,EAAE;IAC5BF,eAAe,CAACC,IAAI,EAAEC,KAAK,CAAC;IAE5B,IAAImB,EAAE,GAAGnB,KAAK,CAACmH,IAAI,CAAC,GAAG,CAAC;IACxBhG,EAAE,GAAGA,EAAE,CAACuF,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,IAAIQ,WAAW,IAAI,KAAK;IAEjD,OAAO,IAAI,CAACT,WAAW,CAACtF,EAAE,CAACiG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1C;EAMAC,gCAAgCA,CAACtH,IAAY,EAAEmH,WAAoB,EAAE;IACnE,OAAOhK,UAAU,CAAC,IAAI,CAAC+J,sBAAsB,CAAClH,IAAI,EAAEmH,WAAW,CAAC,CAAC;EACnE;EAYAI,QAAQA,CAACvH,IAAY,EAAW;IAC9B,IAAIpB,gBAAgB,CAACoB,IAAI,CAAC,IAAIvB,OAAO,CAACuB,IAAI,CAAC,IAAIP,gBAAgB,CAACO,IAAI,CAAC,EAAE;MACrE,OAAO,IAAI;IACb;IAEA,IAAIlC,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMiE,OAAO,GAAG,IAAI,CAACC,UAAU,CAAClE,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAImD,OAAO,EAAE;QACX,OAAOA,OAAO,CAACuD,QAAQ;MACzB,CAAC,MAAM;QACL,OAAO,IAAI,CAACV,UAAU,CAAC9G,IAAI,CAACc,IAAI,CAAC;MACnC;IACF;IAEA,OAAO,KAAK;EACd;EAMA2G,qBAAqBA,CAACzH,IAAY,EAAE0H,QAAkB,EAAE;IACtD,IAAI,IAAI,CAACH,QAAQ,CAACvH,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACb,CAAC,MAAM;MACL,MAAMoB,EAAE,GAAG,IAAI,CAACkG,gCAAgC,CAACtH,IAAI,CAAC;MACtD,IAAI,CAAC0H,QAAQ,EAAE;QACb,IAAI,CAAChH,IAAI,CAAC;UAAEU;QAAG,CAAC,CAAC;QACjB,OAAOnE,SAAS,CAACmE,EAAE,CAAC;MACtB;MACA,OAAOA,EAAE;IACX;EACF;EAEAuG,0BAA0BA,CACxBlH,KAAc,EACdmH,IAAiB,EACjB9G,IAAY,EACZM,EAAO,EACP;IAEA,IAAIwG,IAAI,KAAK,OAAO,EAAE;IAItB,IAAInH,KAAK,CAACmH,IAAI,KAAK,OAAO,EAAE;IAE5B,MAAMC,SAAS,GAEbD,IAAI,KAAK,KAAK,IACdnH,KAAK,CAACmH,IAAI,KAAK,KAAK,IACpBnH,KAAK,CAACmH,IAAI,KAAK,OAAO,IACtBnH,KAAK,CAACmH,IAAI,KAAK,QAAQ,IAEtBnH,KAAK,CAACmH,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAQ;IAE9C,IAAIC,SAAS,EAAE;MACb,MAAM,IAAI,CAACtF,IAAI,CAACuF,GAAG,CAACC,UAAU,CAC5B3G,EAAE,EACF,0BAA0BN,IAAI,GAAG,EACjCkH,SACF,CAAC;IACH;EACF;EAEAC,MAAMA,CACJC,OAAe,EACfC,OAAgB,EAGhB;IACA,MAAMlE,OAAO,GAAG,IAAI,CAACC,UAAU,CAACgE,OAAO,CAAC;IACxC,IAAIjE,OAAO,EAAE;MACXkE,OAAO,KAAPA,OAAO,GAAK,IAAI,CAAC1B,qBAAqB,CAACyB,OAAO,CAAC,CAACpH,IAAI;MACpD,MAAMsH,OAAO,GAAG,IAAIC,gBAAO,CAACpE,OAAO,EAAEiE,OAAO,EAAEC,OAAO,CAAC;MAG/C;QAELC,OAAO,CAACH,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;EACF;EAEAC,IAAIA,CAAA,EAAG;IACL,MAAMC,GAAG,GAAG,GAAG,CAACC,MAAM,CAAC,EAAE,CAAC;IAC1BC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAChB,IAAI7G,KAAY,GAAG,IAAI;IACvB,GAAG;MACD+G,OAAO,CAACC,GAAG,CAAC,GAAG,EAAEhH,KAAK,CAAC+D,KAAK,CAACxF,IAAI,CAAC;MAClC,KAAK,MAAMY,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAAC3C,KAAK,CAACI,QAAQ,CAAC,EAAE;QAC9C,MAAMkC,OAAO,GAAGtC,KAAK,CAACI,QAAQ,CAACjB,IAAI,CAAC;QACpC4H,OAAO,CAACC,GAAG,CAAC,IAAI,EAAE7H,IAAI,EAAE;UACtB0G,QAAQ,EAAEvD,OAAO,CAACuD,QAAQ;UAC1B5F,UAAU,EAAEqC,OAAO,CAACrC,UAAU;UAC9BgH,UAAU,EAAE3E,OAAO,CAACH,kBAAkB,CAACxD,MAAM;UAC7CsH,IAAI,EAAE3D,OAAO,CAAC2D;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,QAASjG,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAC9ByF,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;EAClB;EAEA3B,QAAQA,CAAC/F,IAAY,EAAE;IACrB,OAAO,CAAC,CAAC,IAAI,CAAC+H,QAAQ,CAAC/H,IAAI,CAAC;EAC9B;EAEA+H,QAAQA,CAAC/H,IAAY,EAAE;IACrB,OAAO,IAAI,CAAC8E,MAAM,CAACnD,GAAG,CAAC3B,IAAI,CAAC;EAC9B;EAEAgI,aAAaA,CAACvG,IAAkC,EAAE;IAChD,IAAI,CAACqD,MAAM,CAACK,GAAG,CAAC1D,IAAI,CAACvC,IAAI,CAAC+I,KAAK,CAACjI,IAAI,EAAEyB,IAAI,CAAC;EAC7C;EAEAW,mBAAmBA,CAACX,IAAc,EAAE;IAClC,IAAIA,IAAI,CAACyG,kBAAkB,CAAC,CAAC,EAAE;MAC7B,IAAI,CAACF,aAAa,CAACvG,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAIA,IAAI,CAAC1E,qBAAqB,CAAC,CAAC,EAAE;MACvC,IAAI,CAACiF,eAAe,CAAC,SAAS,EAAEP,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IACvD,CAAC,MAAM,IAAIA,IAAI,CAACzD,qBAAqB,CAAC,CAAC,EAAE;MACvC,MAAMuF,YAAY,GAAG9B,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7C,MAAM;QAAEmF;MAAK,CAAC,GAAGrF,IAAI,CAACvC,IAAI;MAC1B,KAAK,MAAMwC,MAAM,IAAI6B,YAAY,EAAE;QACjC,IAAI,CAACvB,eAAe,CAClB8E,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,aAAa,GAAG,OAAO,GAAGA,IAAI,EAC3DpF,MACF,CAAC;MACH;IACF,CAAC,MAAM,IAAID,IAAI,CAAC9E,kBAAkB,CAAC,CAAC,EAAE;MACpC,IAAI8E,IAAI,CAACvC,IAAI,CAACiJ,OAAO,EAAE;MACvB,IAAI,CAACnG,eAAe,CAAC,KAAK,EAAEP,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIA,IAAI,CAACxE,mBAAmB,CAAC,CAAC,EAAE;MACrC,MAAMmL,iBAAiB,GACrB3G,IAAI,CAACvC,IAAI,CAACmJ,UAAU,KAAK,MAAM,IAAI5G,IAAI,CAACvC,IAAI,CAACmJ,UAAU,KAAK,QAAQ;MACtE,MAAM9I,UAAU,GAAGkC,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC;MACzC,KAAK,MAAM2G,SAAS,IAAI/I,UAAU,EAAE;QAClC,MAAMgJ,eAAe,GACnBH,iBAAiB,IAChBE,SAAS,CAACE,iBAAiB,CAAC,CAAC,KAC3BF,SAAS,CAACpJ,IAAI,CAACmJ,UAAU,KAAK,MAAM,IACnCC,SAAS,CAACpJ,IAAI,CAACmJ,UAAU,KAAK,QAAQ,CAAE;QAE9C,IAAI,CAACrG,eAAe,CAACuG,eAAe,GAAG,SAAS,GAAG,QAAQ,EAAED,SAAS,CAAC;MACzE;IACF,CAAC,MAAM,IAAI7G,IAAI,CAAC3C,mBAAmB,CAAC,CAAC,EAAE;MAErC,MAAM4C,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,aAAa,CAAa;MAClD,IACED,MAAM,CAAC/E,kBAAkB,CAAC,CAAC,IAC3B+E,MAAM,CAAC3E,qBAAqB,CAAC,CAAC,IAC9B2E,MAAM,CAAC1D,qBAAqB,CAAC,CAAC,EAC9B;QACA,IAAI,CAACoE,mBAAmB,CAACV,MAAM,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAI,CAACM,eAAe,CAAC,SAAS,EAAEP,IAAI,CAAC;IACvC;EACF;EAEA1C,kBAAkBA,CAAA,EAAG;IACnB,OAAOA,kBAAkB,CAAC,CAAC;EAC7B;EAEA0J,yBAAyBA,CAAChH,IAAc,EAAE;IACxC,MAAMiH,GAAG,GAAGjH,IAAI,CAACkH,wBAAwB,CAAC,CAAC;IAC3C,KAAK,MAAM3I,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAACkF,GAAG,CAAC,EAAE;MAAA,IAAAE,gBAAA;MACnC,CAAAA,gBAAA,OAAI,CAACxF,UAAU,CAACpD,IAAI,CAAC,aAArB4I,gBAAA,CAAuBC,QAAQ,CAACpH,IAAI,CAAC;IACvC;EACF;EAEAO,eAAeA,CACb8E,IAAqB,EACrBrF,IAAc,EACdqH,WAAqB,GAAGrH,IAAI,EAC5B;IACA,IAAI,CAACqF,IAAI,EAAE,MAAM,IAAIiC,cAAc,CAAC,WAAW,CAAC;IAEhD,IAAItH,IAAI,CAACzD,qBAAqB,CAAC,CAAC,EAAE;MAChC,MAAMgL,WAA4B,GAAGvH,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7D,KAAK,MAAMD,MAAM,IAAIsH,WAAW,EAAE;QAChC,IAAI,CAAChH,eAAe,CAAC8E,IAAI,EAAEpF,MAAM,CAAC;MACpC;MACA;IACF;IAEA,MAAMS,MAAM,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IACtC,MAAM2G,GAAG,GAAGjH,IAAI,CAACwH,0BAA0B,CAAC,IAAI,CAAC;IAEjD,KAAK,MAAMjJ,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAACkF,GAAG,CAAC,EAAE;MACnCvG,MAAM,CAACrB,UAAU,CAACd,IAAI,CAAC,GAAG,IAAI;MAE9B,KAAK,MAAMM,EAAE,IAAIoI,GAAG,CAAC1I,IAAI,CAAC,EAAE;QAC1B,MAAML,KAAK,GAAG,IAAI,CAACuJ,aAAa,CAAClJ,IAAI,CAAC;QAEtC,IAAIL,KAAK,EAAE;UAGT,IAAIA,KAAK,CAACtD,UAAU,KAAKiE,EAAE,EAAE;UAE7B,IAAI,CAACuG,0BAA0B,CAAClH,KAAK,EAAEmH,IAAI,EAAE9G,IAAI,EAAEM,EAAE,CAAC;QACxD;QAGA,IAAIX,KAAK,EAAE;UACTA,KAAK,CAACkJ,QAAQ,CAACC,WAAW,CAAC;QAC7B,CAAC,MAAM;UACL,IAAI,CAAC7H,QAAQ,CAACjB,IAAI,CAAC,GAAG,IAAImJ,gBAAO,CAAC;YAChC9M,UAAU,EAAEiE,EAAE;YACdO,KAAK,EAAE,IAAI;YACXY,IAAI,EAAEqH,WAAW;YACjBhC,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEAsC,SAASA,CAAClK,IAAoC,EAAE;IAC9C,IAAI,CAACgC,OAAO,CAAChC,IAAI,CAACc,IAAI,CAAC,GAAGd,IAAI;EAChC;EAEAmK,MAAMA,CAACrJ,IAAY,EAAW;IAC5B,IAAIa,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAACM,IAAI,CAACnB,IAAI,CAAC,EAAE,OAAO,IAAI;IACnC,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAE9B,OAAO,KAAK;EACd;EAEA8D,SAASA,CAACjG,IAAY,EAAW;IAC/B,IAAIa,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAACK,OAAO,CAAClB,IAAI,CAAC,EAAE,OAAO,IAAI;IACtC,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAE9B,OAAO,KAAK;EACd;EAEA+D,YAAYA,CAAClG,IAAY,EAAW;IAClC,OAAO,CAAC,CAAC,IAAI,CAAC+B,gBAAgB,CAAC,CAAC,CAACjB,UAAU,CAACd,IAAI,CAAC;EACnD;EAEAsJ,MAAMA,CAACpK,IAAY,EAAEqK,aAAuB,EAAW;IACrD,IAAIvM,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMiE,OAAO,GAAG,IAAI,CAACC,UAAU,CAAClE,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAI,CAACmD,OAAO,EAAE,OAAO,KAAK;MAC1B,IAAIoG,aAAa,EAAE,OAAOpG,OAAO,CAACuD,QAAQ;MAC1C,OAAO,IAAI;IACb,CAAC,MAAM,IACL5I,gBAAgB,CAACoB,IAAI,CAAC,IACtBN,cAAc,CAACM,IAAI,CAAC,IACpBP,gBAAgB,CAACO,IAAI,CAAC,IACtBL,aAAa,CAACK,IAAI,CAAC,EACnB;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIzC,OAAO,CAACyC,IAAI,CAAC,EAAE;MAAA,IAAAsK,gBAAA;MACxB,IAAItK,IAAI,CAACuK,UAAU,IAAI,CAAC,IAAI,CAACH,MAAM,CAACpK,IAAI,CAACuK,UAAU,EAAEF,aAAa,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MACA,IAAI,EAAAC,gBAAA,GAAAtK,IAAI,CAACwK,UAAU,qBAAfF,gBAAA,CAAiBhK,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI,CAAC8J,MAAM,CAACpK,IAAI,CAACyK,IAAI,EAAEJ,aAAa,CAAC;IAC9C,CAAC,MAAM,IAAI7M,WAAW,CAACwC,IAAI,CAAC,EAAE;MAC5B,KAAK,MAAM0K,MAAM,IAAI1K,IAAI,CAACyK,IAAI,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACL,MAAM,CAACM,MAAM,EAAEL,aAAa,CAAC,EAAE,OAAO,KAAK;MACvD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIhN,QAAQ,CAAC2C,IAAI,CAAC,EAAE;MACzB,OACE,IAAI,CAACoK,MAAM,CAACpK,IAAI,CAACmB,IAAI,EAAEkJ,aAAa,CAAC,IACrC,IAAI,CAACD,MAAM,CAACpK,IAAI,CAACyD,KAAK,EAAE4G,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAIjN,iBAAiB,CAAC4C,IAAI,CAAC,IAAIT,iBAAiB,CAACS,IAAI,CAAC,EAAE;MAC7D,KAAK,MAAM2K,IAAI,IAAI3K,IAAI,CAAC4K,QAAQ,EAAE;QAChC,IAAID,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAACP,MAAM,CAACO,IAAI,EAAEN,aAAa,CAAC,EAAE,OAAO,KAAK;MACtE;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIhM,kBAAkB,CAAC2B,IAAI,CAAC,IAAIV,kBAAkB,CAACU,IAAI,CAAC,EAAE;MAC/D,KAAK,MAAM6K,IAAI,IAAI7K,IAAI,CAACgB,UAAU,EAAE;QAClC,IAAI,CAAC,IAAI,CAACoJ,MAAM,CAACS,IAAI,EAAER,aAAa,CAAC,EAAE,OAAO,KAAK;MACrD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAInM,QAAQ,CAAC8B,IAAI,CAAC,EAAE;MAAA,IAAA8K,iBAAA;MACzB,IAAI9K,IAAI,CAAC+K,QAAQ,IAAI,CAAC,IAAI,CAACX,MAAM,CAACpK,IAAI,CAACkB,GAAG,EAAEmJ,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAS,iBAAA,GAAA9K,IAAI,CAACwK,UAAU,qBAAfM,iBAAA,CAAiBxK,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIhC,UAAU,CAAC0B,IAAI,CAAC,EAAE;MAAA,IAAAgL,iBAAA;MAE3B,IAAIhL,IAAI,CAAC+K,QAAQ,IAAI,CAAC,IAAI,CAACX,MAAM,CAACpK,IAAI,CAACkB,GAAG,EAAEmJ,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAW,iBAAA,GAAAhL,IAAI,CAACwK,UAAU,qBAAfQ,iBAAA,CAAiB1K,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,IAAId,gBAAgB,CAACQ,IAAI,CAAC,IAAIA,IAAI,CAACiL,MAAM,EAAE;QACzC,IAAIjL,IAAI,CAACW,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAACyJ,MAAM,CAACpK,IAAI,CAACW,KAAK,EAAE0J,aAAa,CAAC,EAAE;UAClE,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIxL,iBAAiB,CAACmB,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI,CAACoK,MAAM,CAACpK,IAAI,CAACiB,QAAQ,EAAEoJ,aAAa,CAAC;IAClD,CAAC,MAAM,IAAI1L,iBAAiB,CAACqB,IAAI,CAAC,EAAE;MAClC,KAAK,MAAMqB,UAAU,IAAIrB,IAAI,CAACkL,WAAW,EAAE;QACzC,IAAI,CAAC,IAAI,CAACd,MAAM,CAAC/I,UAAU,EAAEgJ,aAAa,CAAC,EAAE,OAAO,KAAK;MAC3D;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI3L,0BAA0B,CAACsB,IAAI,CAAC,EAAE;MAC3C,OACEhB,cAAc,CAACgB,IAAI,CAACmL,GAAG,EAAE,YAAY,CAAC,IACtC,CAAC,IAAI,CAACrE,UAAU,CAAC,QAAQ,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/C,IAAI,CAAChB,MAAM,CAACpK,IAAI,CAACqL,KAAK,EAAEhB,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAIpM,kBAAkB,CAAC+B,IAAI,CAAC,EAAE;MACnC,OACE,CAACA,IAAI,CAAC+K,QAAQ,IACdjN,YAAY,CAACkC,IAAI,CAACY,MAAM,CAAC,IACzBZ,IAAI,CAACY,MAAM,CAACE,IAAI,KAAK,QAAQ,IAC7BhD,YAAY,CAACkC,IAAI,CAACa,QAAQ,CAAC,IAC3Bb,IAAI,CAACa,QAAQ,CAACC,IAAI,KAAK,KAAK,IAC5B,CAAC,IAAI,CAACgG,UAAU,CAAC,QAAQ,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC;IAEnD,CAAC,MAAM,IAAI9N,gBAAgB,CAAC0C,IAAI,CAAC,EAAE;MACjC,OACEhB,cAAc,CAACgB,IAAI,CAACe,MAAM,EAAE,YAAY,CAAC,IACzC,CAAC,IAAI,CAAC+F,UAAU,CAAC,QAAQ,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/CpL,IAAI,CAACsI,SAAS,CAAChI,MAAM,KAAK,CAAC,IAC3BzD,CAAC,CAACyO,eAAe,CAACtL,IAAI,CAACsI,SAAS,CAAC,CAAC,CAAC,CAAC;IAExC,CAAC,MAAM;MACL,OAAO/J,SAAS,CAACyB,IAAI,CAAC;IACxB;EACF;EAMAuL,OAAOA,CAACrK,GAAoB,EAAEsK,GAAQ,EAAE;IACtC,OAAQ,IAAI,CAAC3F,IAAI,CAAC3E,GAAG,CAAC,GAAGsK,GAAG;EAC9B;EAMAC,OAAOA,CAACvK,GAAoB,EAAO;IACjC,IAAIS,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMkE,IAAI,GAAGlE,KAAK,CAACkE,IAAI,CAAC3E,GAAG,CAAC;MAC5B,IAAI2E,IAAI,IAAI,IAAI,EAAE,OAAOA,IAAI;IAC/B,CAAC,QAASlE,KAAK,GAAGA,KAAK,CAACsB,MAAM;EAChC;EAOAyI,UAAUA,CAACxK,GAAW,EAAE;IACtB,IAAIS,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMkE,IAAI,GAAGlE,KAAK,CAACkE,IAAI,CAAC3E,GAAG,CAAC;MAC5B,IAAI2E,IAAI,IAAI,IAAI,EAAElE,KAAK,CAACkE,IAAI,CAAC3E,GAAG,CAAC,GAAG,IAAI;IAC1C,CAAC,QAASS,KAAK,GAAGA,KAAK,CAACsB,MAAM;EAChC;EAEA0I,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAAChG,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG,IAAI;MAClB,IAAI,CAACiG,KAAK,CAAC,CAAC;IACd;EACF;EAEAA,KAAKA,CAAA,EAAG;IACN,MAAMrJ,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtBb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI,CAACmE,IAAI,GAAGhE,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAIH,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACmE,QAAQ,EAAE;MACpB,IAAInE,KAAK,CAACY,IAAI,CAACsJ,SAAS,CAAC,CAAC,EAAE;QAC1B;MACF;IACF,CAAC,QAASlK,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAE9B,MAAM6I,aAAa,GAAGnK,KAAK;IAE3B,MAAM4B,KAA0B,GAAG;MACjC3B,UAAU,EAAE,EAAE;MACdkC,kBAAkB,EAAE,EAAE;MACtBW,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACqB,QAAQ,GAAG,IAAI;IACpBR,YAAY,KAAZA,YAAY,GAAKyG,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACvC;MACEzG,KAAKA,CAACjD,IAAI,EAAE;QACVb,UAAU,CAACa,IAAI,CAACZ,KAAK,CAAC;MACxB;IACF,CAAC,EACDU,gBAAgB,CACjB,CAAC;IAGF,IAAIE,IAAI,CAACrC,IAAI,KAAK,SAAS,EAAE;MAC3B,KAAK,MAAMgM,KAAK,IAAI5G,YAAY,CAAC6G,KAAK,EAAE;QACtCD,KAAK,CAACE,IAAI,CAAC7I,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;MAChC;MACA,MAAM8I,YAAY,GAAG/G,YAAY,CAAC/C,IAAI,CAACrC,IAAI,CAAC;MAC5C,IAAImM,YAAY,EAAE;QAChB,KAAK,MAAMH,KAAK,IAAIG,YAAY,CAACF,KAAK,EAAE;UACtCD,KAAK,CAACE,IAAI,CAAC7I,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;QAChC;MACF;IACF;IACAhB,IAAI,CAACwJ,QAAQ,CAACzG,YAAY,EAAE/B,KAAK,CAAC;IAClC,IAAI,CAACuC,QAAQ,GAAG,KAAK;IAGrB,KAAK,MAAMvD,IAAI,IAAIgB,KAAK,CAACkB,WAAW,EAAE;MAEpC,MAAM+E,GAAG,GAAGjH,IAAI,CAACkH,wBAAwB,CAAC,CAAC;MAC3C,KAAK,MAAM3I,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAACkF,GAAG,CAAC,EAAE;QACnC,IAAIjH,IAAI,CAACZ,KAAK,CAACuC,UAAU,CAACpD,IAAI,CAAC,EAAE;QACjCgL,aAAa,CAAC5B,SAAS,CAACV,GAAG,CAAC1I,IAAI,CAAC,CAAC;MACpC;MAGAyB,IAAI,CAACZ,KAAK,CAAC4H,yBAAyB,CAAChH,IAAI,CAAC;IAC5C;IAGA,KAAK,MAAM+J,GAAG,IAAI/I,KAAK,CAAC3B,UAAU,EAAE;MAClC,MAAMqC,OAAO,GAAGqI,GAAG,CAAC3K,KAAK,CAACuC,UAAU,CAACoI,GAAG,CAACtM,IAAI,CAACc,IAAI,CAAC;MACnD,IAAImD,OAAO,EAAE;QACXA,OAAO,CAACE,SAAS,CAACmI,GAAG,CAAC;MACxB,CAAC,MAAM;QACLR,aAAa,CAAC5B,SAAS,CAACoC,GAAG,CAACtM,IAAI,CAAC;MACnC;IACF;IAGA,KAAK,MAAMuC,IAAI,IAAIgB,KAAK,CAACO,kBAAkB,EAAE;MAC3CvB,IAAI,CAACZ,KAAK,CAAC4H,yBAAyB,CAAChH,IAAI,CAAC;IAC5C;EACF;EAEA7B,IAAIA,CAAC6L,IAMJ,EAAE;IACD,IAAIhK,IAAI,GAAG,IAAI,CAACA,IAAI;IAEpB,IAAIA,IAAI,CAACsB,SAAS,CAAC,CAAC,EAAE;MACpBtB,IAAI,GAAG,IAAI,CAACiK,gBAAgB,CAAC,CAAC,CAACjK,IAAI;IACrC,CAAC,MAAM,IAAI,CAACA,IAAI,CAACkK,gBAAgB,CAAC,CAAC,IAAI,CAAClK,IAAI,CAACsJ,SAAS,CAAC,CAAC,EAAE;MACxDtJ,IAAI,GAAG,IAAI,CAACa,cAAc,CAAC,CAAC,CAACb,IAAI;IACnC;IAEA,IAAIA,IAAI,CAACmK,iBAAiB,CAAC,CAAC,EAAE;MAC5BnK,IAAI,GAAG,CAAC,IAAI,CAACK,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAEN,IAAI;IACnE;IAEA,MAAM;MAAEoJ,IAAI;MAAEgB,MAAM;MAAE/E,IAAI,GAAG,KAAK;MAAExG;IAAG,CAAC,GAAGmL,IAAI;IAM/C,IACE,CAACZ,IAAI,IACL,CAACgB,MAAM,KACN/E,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,CAAC,IAClCrF,IAAI,CAACqK,UAAU,CAAC,CAAC,IAEjB,CAACrK,IAAI,CAACvC,IAAI,CAACc,IAAI,IACfxD,gBAAgB,CAACiF,IAAI,CAACU,MAAM,EAAE;MAAElC,MAAM,EAAEwB,IAAI,CAACvC;IAAK,CAAC,CAAC,IACpDuC,IAAI,CAACU,MAAM,CAACqF,SAAS,CAAChI,MAAM,IAAIiC,IAAI,CAACvC,IAAI,CAACgF,MAAM,CAAC1E,MAAM,IACvDxC,YAAY,CAACsD,EAAE,CAAC,EAChB;MACAmB,IAAI,CAACsK,aAAa,CAAC,QAAQ,EAAEzL,EAAE,CAAC;MAChCmB,IAAI,CAACZ,KAAK,CAACmB,eAAe,CACxB,OAAO,EACPP,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC,CAACF,IAAI,CAACvC,IAAI,CAACgF,MAAM,CAAC1E,MAAM,GAAG,CAAC,CAChD,CAAC;MACD;IACF;IAEA,IAAIiC,IAAI,CAACuK,MAAM,CAAC,CAAC,IAAIvK,IAAI,CAACwK,aAAa,CAAC,CAAC,IAAIxK,IAAI,CAACqK,UAAU,CAAC,CAAC,EAAE;MAC9DrK,IAAI,CAACyK,WAAW,CAAC,CAAC;MAClBzK,IAAI,GAAGA,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IACzB;IAEA,MAAMwK,UAAU,GAAGV,IAAI,CAACW,WAAW,IAAI,IAAI,GAAG,CAAC,GAAGX,IAAI,CAACW,WAAW;IAElE,MAAMC,OAAO,GAAG,eAAevF,IAAI,IAAIqF,UAAU,EAAE;IACnD,IAAIG,UAAU,GAAG,CAACT,MAAM,IAAIpK,IAAI,CAACkJ,OAAO,CAAC0B,OAAO,CAAC;IAEjD,IAAI,CAACC,UAAU,EAAE;MACf,MAAM5K,MAAM,GAAGpD,mBAAmB,CAACwI,IAAI,EAAE,EAAE,CAAC;MAE5CpF,MAAM,CAAC0K,WAAW,GAAGD,UAAU;MAE/B,CAACG,UAAU,CAAC,GAAI7K,IAAI,CAAgC8K,gBAAgB,CAClE,MAAM,EACN,CAAC7K,MAAM,CACT,CAAC;MACD,IAAI,CAACmK,MAAM,EAAEpK,IAAI,CAACgJ,OAAO,CAAC4B,OAAO,EAAEC,UAAU,CAAC;IAChD;IAEA,MAAME,UAAU,GAAGjO,kBAAkB,CAAC+B,EAAE,EAAEuK,IAAI,CAAC;IAC/C,MAAM4B,GAAG,GAAGH,UAAU,CAACpN,IAAI,CAACqE,YAAY,CAAC3D,IAAI,CAAC4M,UAAU,CAAC;IACzD/K,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAAC8E,IAAI,EAAEwF,UAAU,CAAC3K,GAAG,CAAC,cAAc,CAAC,CAAC8K,GAAG,GAAG,CAAC,CAAC,CAAC;EAC3E;EAMA1K,gBAAgBA,CAAA,EAAG;IACjB,IAAIlB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACY,IAAI,CAACsJ,SAAS,CAAC,CAAC,EAAE;QAC1B,OAAOlK,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAC9B,MAAM,IAAIuK,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAMA5K,iBAAiBA,CAAA,EAAiB;IAChC,IAAIjB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACY,IAAI,CAACkL,gBAAgB,CAAC,CAAC,EAAE;QACjC,OAAO9L,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAC9B,OAAO,IAAI;EACb;EAOAG,cAAcA,CAAA,EAAG;IACf,IAAIzB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACY,IAAI,CAACmL,aAAa,CAAC,CAAC,EAAE;QAC9B,OAAO/L,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAC9B,MAAM,IAAIuK,KAAK,CACb,8EACF,CAAC;EACH;EAOAhB,gBAAgBA,CAAA,EAAG;IACjB,IAAI7K,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAI,CAACA,KAAK,CAACY,IAAI,CAACsB,SAAS,CAAC,CAAC,EAAE;QAC3B,OAAOlC,KAAK,CAACyB,cAAc,CAAC,CAAC;MAC/B;IACF,CAAC,QAASzB,KAAK,GAAGA,KAAK,CAACsB,MAAM,CAACA,MAAM;IACrC,MAAM,IAAIuK,KAAK,CACb,8EACF,CAAC;EACH;EAMAG,cAAcA,CAAA,EAA4B;IACxC,MAAMnE,GAAG,GAAG3H,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAIH,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,KAAK,MAAMT,GAAG,IAAIW,MAAM,CAACyC,IAAI,CAAC3C,KAAK,CAACI,QAAQ,CAAC,EAAE;QAC7C,IAAIb,GAAG,IAAIsI,GAAG,KAAK,KAAK,EAAE;UACxBA,GAAG,CAACtI,GAAG,CAAC,GAAGS,KAAK,CAACI,QAAQ,CAACb,GAAG,CAAC;QAChC;MACF;MACAS,KAAK,GAAGA,KAAK,CAACsB,MAAM;IACtB,CAAC,QAAQtB,KAAK;IAEd,OAAO6H,GAAG;EACZ;EAEAoE,uBAAuBA,CAAC9M,IAAY,EAAEd,IAAY,EAAW;IAC3D,OAAO,IAAI,CAAC6N,oBAAoB,CAAC/M,IAAI,CAAC,KAAKd,IAAI;EACjD;EAEAkE,UAAUA,CAACpD,IAAY,EAAuB;IAC5C,IAAIa,KAAY,GAAG,IAAI;IACvB,IAAImM,YAAY;IAEhB,GAAG;MACD,MAAM7J,OAAO,GAAGtC,KAAK,CAACqI,aAAa,CAAClJ,IAAI,CAAC;MACzC,IAAImD,OAAO,EAAE;QAAA,IAAA8J,aAAA;QAUX,IACE,CAAAA,aAAA,GAAAD,YAAY,aAAZC,aAAA,CAAclK,SAAS,CAAC,CAAC,IACzBI,OAAO,CAAC2D,IAAI,KAAK,OAAO,IACxB3D,OAAO,CAAC2D,IAAI,KAAK,OAAO,EACxB,CAEF,CAAC,MAAM;UACL,OAAO3D,OAAO;QAChB;MACF,CAAC,MAAM,IACL,CAACA,OAAO,IACRnD,IAAI,KAAK,WAAW,IACpBa,KAAK,CAACY,IAAI,CAACqK,UAAU,CAAC,CAAC,IACvB,CAACjL,KAAK,CAACY,IAAI,CAACyL,yBAAyB,CAAC,CAAC,EACvC;QACA;MACF;MACAF,YAAY,GAAGnM,KAAK,CAACY,IAAI;IAC3B,CAAC,QAASZ,KAAK,GAAGA,KAAK,CAACsB,MAAM;EAChC;EAEA+G,aAAaA,CAAClJ,IAAY,EAAuB;IAC/C,OAAO,IAAI,CAACiB,QAAQ,CAACjB,IAAI,CAAC;EAC5B;EAGA+M,oBAAoBA,CAAC/M,IAAY,EAAgB;IAAA,IAAAmN,iBAAA;IAC/C,QAAAA,iBAAA,GAAO,IAAI,CAAC/J,UAAU,CAACpD,IAAI,CAAC,qBAArBmN,iBAAA,CAAuB9Q,UAAU;EAC1C;EAGA+Q,uBAAuBA,CAACpN,IAAY,EAAgB;IAClD,MAAMmD,OAAO,GAAG,IAAI,CAAClC,QAAQ,CAACjB,IAAI,CAAC;IACnC,OAAOmD,OAAO,oBAAPA,OAAO,CAAE9G,UAAU;EAC5B;EAEAgR,aAAaA,CAACrN,IAAY,EAAE;IAC1B,OAAO,CAAC,CAAC,IAAI,CAACkJ,aAAa,CAAClJ,IAAI,CAAC;EACnC;EAQAgG,UAAUA,CACRhG,IAAY,EACZyL,IAEgE,EAChE;IACA,IAAI,CAACzL,IAAI,EAAE,OAAO,KAAK;IAEvB,IAAIsK,SAAS;IACb,IAAIgD,MAAM;IACV,IAAIC,SAAS;IACb,IAAI,OAAO9B,IAAI,KAAK,QAAQ,EAAE;MAC5BnB,SAAS,GAAGmB,IAAI,CAACnB,SAAS;MAC1BgD,MAAM,GAAG7B,IAAI,CAAC6B,MAAM;MACpBC,SAAS,GAAG9B,IAAI,CAAC8B,SAAS;IAC5B,CAAC,MAAM,IAAI,OAAO9B,IAAI,KAAK,SAAS,EAAE;MACpCnB,SAAS,GAAGmB,IAAI;IAClB;IACA,IAAI5K,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAI0M,SAAS,KAAK1M,KAAK,EAAE;QACvB;MACF;MACA,IAAIA,KAAK,CAACwM,aAAa,CAACrN,IAAI,CAAC,EAAE;QAC7B,OAAO,IAAI;MACb;IACF,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAE9B,IAAI,CAACmL,MAAM,IAAI,IAAI,CAACjE,MAAM,CAACrJ,IAAI,CAAC,EAAE,OAAO,IAAI;IAC7C,IAAI,CAACsK,SAAS,IAAI5F,KAAK,CAACxD,OAAO,CAACsM,QAAQ,CAACxN,IAAI,CAAC,EAAE,OAAO,IAAI;IAC3D,IAAI,CAACsK,SAAS,IAAI5F,KAAK,CAAC+I,gBAAgB,CAACD,QAAQ,CAACxN,IAAI,CAAC,EAAE,OAAO,IAAI;IACpE,OAAO,KAAK;EACd;EAEA0N,gBAAgBA,CACd1N,IAAY,EACZyL,IAAgD,EAChD;IAAA,IAAAkC,YAAA;IACA,QAAAA,YAAA,GAAO,IAAI,CAACxL,MAAM,qBAAXwL,YAAA,CAAa3H,UAAU,CAAChG,IAAI,EAAEyL,IAAI,CAAC;EAC5C;EAMAmC,aAAaA,CAAC5N,IAAY,EAAEa,KAAY,EAAE;IACxC,MAAMgN,IAAI,GAAG,IAAI,CAACzK,UAAU,CAACpD,IAAI,CAAC;IAClC,IAAI6N,IAAI,EAAE;MACRA,IAAI,CAAChN,KAAK,CAACiN,gBAAgB,CAAC9N,IAAI,CAAC;MACjC6N,IAAI,CAAChN,KAAK,GAAGA,KAAK;MAClBA,KAAK,CAACI,QAAQ,CAACjB,IAAI,CAAC,GAAG6N,IAAI;IAC7B;EACF;EAEAC,gBAAgBA,CAAC9N,IAAY,EAAE;IAC7B,OAAO,IAAI,CAACiB,QAAQ,CAACjB,IAAI,CAAC;EAC5B;EAEA+N,aAAaA,CAAC/N,IAAY,EAAE;IAAA,IAAAgO,iBAAA;IAE1B,CAAAA,iBAAA,OAAI,CAAC5K,UAAU,CAACpD,IAAI,CAAC,aAArBgO,iBAAA,CAAuBnN,KAAK,CAACiN,gBAAgB,CAAC9N,IAAI,CAAC;IAGnD,IAAIa,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACM,IAAI,CAACnB,IAAI,CAAC,EAAE;QACpBa,KAAK,CAACM,IAAI,CAACnB,IAAI,CAAC,GAAG,KAAK;MAC1B;IACF,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACsB,MAAM;EAChC;EAYA8L,cAAcA,CACZC,IAAkD,GAAG5N,EAAE,IACrD,IAAI,CAACV,IAAI,CAAC;IAAEU;EAAG,CAAC,CAAC,EACnB;IACA,IAAI,CAACwK,KAAK,CAAC,CAAC;IAEZ,MAAMqD,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,KAAK,MAAMpO,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAAC,IAAI,CAACvC,QAAQ,CAAC,EAAE;MAC7C,MAAMkC,OAAO,GAAG,IAAI,CAAClC,QAAQ,CAACjB,IAAI,CAAC;MACnC,IAAI,CAACmD,OAAO,EAAE;MACd,MAAM;QAAE1B;MAAK,CAAC,GAAG0B,OAAO;MACxB,IAAI,CAAC1B,IAAI,CAAC4M,oBAAoB,CAAC,CAAC,EAAE;MAClC,MAAM;QAAElM,MAAM;QAAES;MAAW,CAAC,GAAGnB,IAAI;MAEnC,IAAIU,MAAM,CAAC2E,IAAI,KAAK,KAAK,IAAIqH,IAAI,CAACG,GAAG,CAACnM,MAAM,CAAC,EAAE;MAC/CgM,IAAI,CAACI,GAAG,CAAC9M,IAAI,CAACU,MAAM,CAAC;MAErB,IAAIqM,OAAO;MACX,MAAM3D,IAAI,GAAG,EAAE;MACf,KAAK,MAAMvH,IAAI,IAAInB,MAAM,CAACoB,YAAY,EAAE;QACtCiL,OAAO,WAAPA,OAAO,GAAPA,OAAO,GAAKlL,IAAI,CAAChD,EAAE;QACnB,IAAIgD,IAAI,CAACuH,IAAI,EAAE;UACbA,IAAI,CAACjL,IAAI,CAAC3D,oBAAoB,CAAC,GAAG,EAAEqH,IAAI,CAAChD,EAAE,EAAEgD,IAAI,CAACuH,IAAI,CAAC,CAAC;QAC1D;QAEA,MAAMnC,GAAG,GAAG3H,MAAM,CAACyC,IAAI,CAACpH,qBAAqB,CAACkH,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvE,KAAK,MAAMtD,IAAI,IAAI0I,GAAG,EAAE;UACtBwF,IAAI,CAAC7R,UAAU,CAAC2D,IAAI,CAAC,EAAEsD,IAAI,CAACuH,IAAI,IAAI,IAAI,CAAC;QAC3C;MACF;MAGA,IAAIjI,UAAU,CAACA,UAAU,CAAC6L,KAAK,CAAC;QAAEpO,IAAI,EAAE8B;MAAO,CAAC,CAAC,EAAE;QACjDS,UAAU,CAAC8L,WAAW,CAACF,OAAO,CAAC;MACjC,CAAC,MAAM,IAAI3D,IAAI,CAACrL,MAAM,KAAK,CAAC,EAAE;QAC5BoD,UAAU,CAAC+L,MAAM,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,MAAMC,IAAI,GAAG/D,IAAI,CAACrL,MAAM,KAAK,CAAC,GAAGqL,IAAI,CAAC,CAAC,CAAC,GAAG7L,kBAAkB,CAAC6L,IAAI,CAAC;QACnE,IAAIjI,UAAU,CAACA,UAAU,CAACiM,cAAc,CAAC;UAAEhE,IAAI,EAAE1I;QAAO,CAAC,CAAC,EAAE;UAC1DS,UAAU,CAAC8L,WAAW,CAACE,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLhM,UAAU,CAAC8L,WAAW,CAACzQ,mBAAmB,CAAC2Q,IAAI,CAAC,CAAC;QACnD;MACF;IACF;EACF;AACF;AAACE,OAAA,CAAAC,OAAA,GAAArK,KAAA;AAn7BKA,KAAK,CA2CFxD,OAAO,GAAGH,MAAM,CAACyC,IAAI,CAACtC,QAAO,CAAC8N,OAAO,CAAC;AA3CzCtK,KAAK,CAiDF+I,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC;AAo4B1B;EAG7C/I,KAAK,CAACuK,SAAS,CAACC,cAAc,GAAG,SAASA,cAAcA,CACtDC,GAAqC,EACrC/H,OAAwB,EACxBC,OAAwB,EACxBxH,KAAc,EACd;IACA,IAAIsP,GAAG,CAAC/H,OAAO,CAAC,EAAE;MAChB+H,GAAG,CAAC9H,OAAO,CAAC,GAAGxH,KAAK;MACpBsP,GAAG,CAAC/H,OAAO,CAAC,GAAG,IAAI;IACrB;EACF,CAAC;EAcD1C,KAAK,CAACuK,SAAS,CAAChE,QAAQ,GAAG,UAEzB/L,IAAS,EACTuM,IAAS,EACThJ,KAAS,EACT;IACA,IAAAwI,cAAQ,EAAC/L,IAAI,EAAEuM,IAAI,EAAE,IAAI,EAAEhJ,KAAK,EAAE,IAAI,CAAChB,IAAI,CAAC;EAC9C,CAAC;EAMDiD,KAAK,CAACuK,SAAS,CAACG,YAAY,GAAG,SAASA,YAAYA,CAClDpP,IAAY,EACZ8F,CAAS,EACT;IACA,IAAIxF,EAAE,GAAGN,IAAI;IACb,IAAI8F,CAAC,GAAG,CAAC,EAAExF,EAAE,IAAIwF,CAAC;IAClB,OAAO,IAAIxF,EAAE,EAAE;EACjB,CAAC;EAIDoE,KAAK,CAACuK,SAAS,CAACI,OAAO,GAAG,SAASA,OAAOA,CAExCnQ,IAAY,EACZ4G,CAAoB,EACpBwJ,mBAAoC,EACpC;IACA,IAAItS,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMiE,OAAO,GAAG,IAAI,CAACC,UAAU,CAAClE,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAImD,OAAO,YAAPA,OAAO,CAAEuD,QAAQ,IAAIvD,OAAO,CAAC1B,IAAI,CAAC8N,aAAa,CAAC,OAAO,CAAC,EAAE;QAC5D,OAAOrQ,IAAI;MACb;IACF;IAEA,IAAI5C,iBAAiB,CAAC4C,IAAI,CAAC,EAAE;MAC3B,OAAOA,IAAI;IACb;IAEA,IAAIlC,YAAY,CAACkC,IAAI,EAAE;MAAEc,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;MAC7C,OAAO9D,cAAc,CACnBiC,gBAAgB,CACdA,gBAAgB,CACdA,gBAAgB,CAAC9B,UAAU,CAAC,OAAO,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC,EAC9DA,UAAU,CAAC,OAAO,CACpB,CAAC,EACDA,UAAU,CAAC,MAAM,CACnB,CAAC,EACD,CAAC6C,IAAI,CACP,CAAC;IACH;IAEA,IAAIsQ,UAAU;IACd,MAAMC,IAAI,GAAG,CAACvQ,IAAI,CAAC;IACnB,IAAI4G,CAAC,KAAK,IAAI,EAAE;MAEd0J,UAAU,GAAG,mBAAmB;IAClC,CAAC,MAAM,IAAI,OAAO1J,CAAC,KAAK,QAAQ,EAAE;MAChC2J,IAAI,CAAC7P,IAAI,CAACxB,cAAc,CAAC0H,CAAC,CAAC,CAAC;MAG5B0J,UAAU,GAAG,eAAe;IAE9B,CAAC,MAAM;MAELA,UAAU,GAAG,SAAS;IACxB;IAEA,IAAIF,mBAAmB,EAAE;MACvBG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACjO,IAAI,CAACuF,GAAG,CAAC2I,SAAS,CAACH,UAAU,CAAC,CAAC;MACjDA,UAAU,GAAG,gBAAgB;IAC/B;IAGA,OAAOtT,cAAc,CAAC,IAAI,CAACuF,IAAI,CAACuF,GAAG,CAAC2I,SAAS,CAACH,UAAU,CAAC,EAAEC,IAAI,CAAC;EAClE,CAAC;EAMD/K,KAAK,CAACuK,SAAS,CAACW,oBAAoB,GAAG,SAASA,oBAAoBA,CAClE,GAAGC,KAAe,EACO;IACzB,MAAMnH,GAAG,GAAG3H,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE/B,KAAK,MAAM8F,IAAI,IAAI+I,KAAK,EAAE;MACxB,IAAIhP,KAAY,GAAG,IAAI;MACvB,GAAG;QACD,KAAK,MAAMb,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAAC3C,KAAK,CAACI,QAAQ,CAAC,EAAE;UAC9C,MAAMkC,OAAO,GAAGtC,KAAK,CAACI,QAAQ,CAACjB,IAAI,CAAC;UACpC,IAAImD,OAAO,CAAC2D,IAAI,KAAKA,IAAI,EAAE4B,GAAG,CAAC1I,IAAI,CAAC,GAAGmD,OAAO;QAChD;QACAtC,KAAK,GAAGA,KAAK,CAACsB,MAAM;MACtB,CAAC,QAAQtB,KAAK;IAChB;IAEA,OAAO6H,GAAG;EACZ,CAAC;EAED3H,MAAM,CAAC+O,gBAAgB,CAACpL,KAAK,CAACuK,SAAS,EAAE;IACvCc,WAAW,EAAE;MACXC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBtO,GAAGA,CAAA,EAAc;QACf,OAAO,IAAI,CAACF,IAAI,CAACU,MAAM;MACzB;IACF,CAAC;IACD6E,GAAG,EAAE;MACHgJ,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBtO,GAAGA,CAAA,EAAc;QACf,OAAO,IAAI,CAACF,IAAI,CAACuF,GAAG;MACtB;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}
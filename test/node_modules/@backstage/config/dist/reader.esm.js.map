{"version": 3, "file": "reader.esm.js", "sources": ["../src/reader.ts"], "sourcesContent": ["/*\n * Copyright 2020 The Backstage Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { JsonValue, JsonObject } from '@backstage/types';\nimport { AppConfig, Config } from './types';\n\n// Update the same pattern in config-loader package if this is changed\nconst CONFIG_KEY_PART_PATTERN = /^[a-z][a-z0-9]*(?:[-_][a-z][a-z0-9]*)*$/i;\n\nfunction isObject(value: JsonValue | undefined): value is JsonObject {\n  return typeof value === 'object' && value !== null && !Array.isArray(value);\n}\n\nfunction cloneDeep(value: JsonValue | null | undefined): JsonValue | undefined {\n  if (typeof value !== 'object' || value === null) {\n    return value;\n  }\n  if (Array.isArray(value)) {\n    return value.map(cloneDeep) as JsonValue;\n  }\n  return Object.fromEntries(\n    Object.entries(value).map(([k, v]) => [k, cloneDeep(v)]),\n  );\n}\n\nfunction merge(\n  into: JsonValue | undefined,\n  from?: JsonValue | undefined,\n): JsonValue | undefined {\n  if (into === null) {\n    return undefined;\n  }\n  if (into === undefined) {\n    return from === undefined ? undefined : merge(from);\n  }\n  if (typeof into !== 'object' || Array.isArray(into)) {\n    return into;\n  }\n  const fromObj = isObject(from) ? from : {};\n\n  const out: JsonObject = {};\n  for (const key of new Set([...Object.keys(into), ...Object.keys(fromObj)])) {\n    const val = merge(into[key], fromObj[key]);\n    if (val !== undefined) {\n      out[key] = val;\n    }\n  }\n  return out;\n}\n\nfunction typeOf(value: JsonValue | undefined): string {\n  if (value === null) {\n    return 'null';\n  } else if (Array.isArray(value)) {\n    return 'array';\n  }\n  const type = typeof value;\n  if (type === 'number' && isNaN(value as number)) {\n    return 'nan';\n  }\n  if (type === 'string' && value === '') {\n    return 'empty-string';\n  }\n  return type;\n}\n\n// Separate out a couple of common error messages to reduce bundle size.\nconst errors = {\n  type(key: string, context: string, typeName: string, expected: string) {\n    return `Invalid type in config for key '${key}' in '${context}', got ${typeName}, wanted ${expected}`;\n  },\n  missing(key: string, context: string) {\n    return `Missing required config value at '${key}' in '${context}'`;\n  },\n  convert(key: string, context: string, expected: string) {\n    return `Unable to convert config value for key '${key}' in '${context}' to a ${expected}`;\n  },\n};\n\n/**\n * An implementation of the `Config` interface that uses a plain JavaScript object\n * for the backing data, with the ability of linking multiple readers together.\n *\n * @public\n */\nexport class ConfigReader implements Config {\n  /**\n   * A set of key paths that where removed from the config due to not being visible.\n   *\n   * This was added as a mutable private member to avoid changes to the public API.\n   * Its only purpose of this is to warn users of missing visibility when running\n   * the frontend in development mode.\n   */\n  private filteredKeys?: string[];\n  private notifiedFilteredKeys = new Set<string>();\n\n  /**\n   * Instantiates the config reader from a list of application config objects.\n   */\n  static fromConfigs(configs: AppConfig[]): ConfigReader {\n    if (configs.length === 0) {\n      return new ConfigReader(undefined);\n    }\n\n    // Merge together all configs into a single config with recursive fallback\n    // readers, giving the first config object in the array the lowest priority.\n    return configs.reduce<ConfigReader>(\n      (previousReader, { data, context, filteredKeys, deprecatedKeys }) => {\n        const reader = new ConfigReader(data, context, previousReader);\n        reader.filteredKeys = filteredKeys;\n\n        if (deprecatedKeys) {\n          for (const { key, description } of deprecatedKeys) {\n            // eslint-disable-next-line no-console\n            console.warn(\n              `The configuration key '${key}' of ${context} is deprecated and may be removed soon. ${\n                description || ''\n              }`,\n            );\n          }\n        }\n\n        return reader;\n      },\n      undefined!,\n    );\n  }\n\n  constructor(\n    private readonly data: JsonObject | undefined,\n    private readonly context: string = 'mock-config',\n    private readonly fallback?: ConfigReader,\n    private readonly prefix: string = '',\n  ) {}\n\n  /** {@inheritdoc Config.has} */\n  has(key: string): boolean {\n    const value = this.readValue(key);\n    if (value === null) {\n      return false;\n    }\n    if (value !== undefined) {\n      return true;\n    }\n    return this.fallback?.has(key) ?? false;\n  }\n\n  /** {@inheritdoc Config.keys} */\n  keys(): string[] {\n    const localKeys = this.data ? Object.keys(this.data) : [];\n    const fallbackKeys = this.fallback?.keys() ?? [];\n    return [...new Set([...localKeys, ...fallbackKeys])].filter(\n      k => this.data?.[k] !== null,\n    );\n  }\n\n  /** {@inheritdoc Config.get} */\n  get<T = JsonValue>(key?: string): T {\n    const value = this.getOptional(key);\n    if (value === undefined) {\n      throw new Error(errors.missing(this.fullKey(key ?? ''), this.context));\n    }\n    return value as T;\n  }\n\n  /** {@inheritdoc Config.getOptional} */\n  getOptional<T = JsonValue>(key?: string): T | undefined {\n    const value = cloneDeep(this.readValue(key));\n    const fallbackValue = this.fallback?.getOptional(key);\n\n    if (value === null) {\n      return undefined;\n    }\n    if (value === undefined) {\n      if (process.env.NODE_ENV === 'development') {\n        if (fallbackValue === undefined && key) {\n          const fullKey = this.fullKey(key);\n          if (\n            this.filteredKeys?.includes(fullKey) &&\n            !this.notifiedFilteredKeys.has(fullKey)\n          ) {\n            this.notifiedFilteredKeys.add(fullKey);\n            // eslint-disable-next-line no-console\n            console.warn(\n              `Failed to read configuration value at '${fullKey}' as it is not visible. ` +\n                'See https://backstage.io/docs/conf/defining#visibility for instructions on how to make it visible.',\n            );\n          }\n        }\n      }\n      return merge(fallbackValue) as T;\n    } else if (fallbackValue === undefined) {\n      return merge(value) as T;\n    }\n\n    return merge(value, fallbackValue) as T;\n  }\n\n  /** {@inheritdoc Config.getConfig} */\n  getConfig(key: string): ConfigReader {\n    const value = this.getOptionalConfig(key);\n    if (value === undefined) {\n      throw new Error(errors.missing(this.fullKey(key), this.context));\n    }\n    return value;\n  }\n\n  /** {@inheritdoc Config.getOptionalConfig} */\n  getOptionalConfig(key: string): ConfigReader | undefined {\n    const value = this.readValue(key);\n    const fallbackConfig = this.fallback?.getOptionalConfig(key);\n\n    if (isObject(value)) {\n      return this.copy(value, key, fallbackConfig);\n    }\n    if (value === null) {\n      return undefined;\n    }\n    if (value !== undefined) {\n      throw new TypeError(\n        errors.type(this.fullKey(key), this.context, typeOf(value), 'object'),\n      );\n    }\n    return fallbackConfig;\n  }\n\n  /** {@inheritdoc Config.getConfigArray} */\n  getConfigArray(key: string): ConfigReader[] {\n    const value = this.getOptionalConfigArray(key);\n    if (value === undefined) {\n      throw new Error(errors.missing(this.fullKey(key), this.context));\n    }\n    return value;\n  }\n\n  /** {@inheritdoc Config.getOptionalConfigArray} */\n  getOptionalConfigArray(key: string): ConfigReader[] | undefined {\n    const configs = this.readConfigValue<JsonObject[]>(key, values => {\n      if (!Array.isArray(values)) {\n        return { expected: 'object-array' };\n      }\n\n      for (const [index, value] of values.entries()) {\n        if (!isObject(value)) {\n          return { expected: 'object-array', value, key: `${key}[${index}]` };\n        }\n      }\n      return true;\n    });\n\n    if (!configs) {\n      if (process.env.NODE_ENV === 'development') {\n        const fullKey = this.fullKey(key);\n        if (\n          this.filteredKeys?.some(k => k.startsWith(fullKey)) &&\n          !this.notifiedFilteredKeys.has(key)\n        ) {\n          this.notifiedFilteredKeys.add(key);\n          // eslint-disable-next-line no-console\n          console.warn(\n            `Failed to read configuration array at '${key}' as it does not have any visible elements. ` +\n              'See https://backstage.io/docs/conf/defining#visibility for instructions on how to make it visible.',\n          );\n        }\n      }\n      return undefined;\n    }\n\n    return configs.map((obj, index) => this.copy(obj, `${key}[${index}]`));\n  }\n\n  /** {@inheritdoc Config.getNumber} */\n  getNumber(key: string): number {\n    const value = this.getOptionalNumber(key);\n    if (value === undefined) {\n      throw new Error(errors.missing(this.fullKey(key), this.context));\n    }\n    return value;\n  }\n\n  /** {@inheritdoc Config.getOptionalNumber} */\n  getOptionalNumber(key: string): number | undefined {\n    const value = this.readConfigValue<string | number>(\n      key,\n      val =>\n        typeof val === 'number' ||\n        typeof val === 'string' || { expected: 'number' },\n    );\n    if (typeof value === 'number' || value === undefined) {\n      return value;\n    }\n    const number = Number(value);\n    if (!Number.isFinite(number)) {\n      throw new Error(\n        errors.convert(this.fullKey(key), this.context, 'number'),\n      );\n    }\n    return number;\n  }\n\n  /** {@inheritdoc Config.getBoolean} */\n  getBoolean(key: string): boolean {\n    const value = this.getOptionalBoolean(key);\n    if (value === undefined) {\n      throw new Error(errors.missing(this.fullKey(key), this.context));\n    }\n    return value;\n  }\n\n  /** {@inheritdoc Config.getOptionalBoolean} */\n  getOptionalBoolean(key: string): boolean | undefined {\n    const value = this.readConfigValue<string | number | boolean>(\n      key,\n      val =>\n        typeof val === 'boolean' ||\n        typeof val === 'number' ||\n        typeof val === 'string' || { expected: 'boolean' },\n    );\n    if (typeof value === 'boolean' || value === undefined) {\n      return value;\n    }\n    const valueString = String(value).trim();\n\n    if (/^(?:y|yes|true|1|on)$/i.test(valueString)) {\n      return true;\n    }\n    if (/^(?:n|no|false|0|off)$/i.test(valueString)) {\n      return false;\n    }\n    throw new Error(errors.convert(this.fullKey(key), this.context, 'boolean'));\n  }\n\n  /** {@inheritdoc Config.getString} */\n  getString(key: string): string {\n    const value = this.getOptionalString(key);\n    if (value === undefined) {\n      throw new Error(errors.missing(this.fullKey(key), this.context));\n    }\n    return value;\n  }\n\n  /** {@inheritdoc Config.getOptionalString} */\n  getOptionalString(key: string): string | undefined {\n    return this.readConfigValue(\n      key,\n      value =>\n        (typeof value === 'string' && value !== '') || { expected: 'string' },\n    );\n  }\n\n  /** {@inheritdoc Config.getStringArray} */\n  getStringArray(key: string): string[] {\n    const value = this.getOptionalStringArray(key);\n    if (value === undefined) {\n      throw new Error(errors.missing(this.fullKey(key), this.context));\n    }\n    return value;\n  }\n\n  /** {@inheritdoc Config.getOptionalStringArray} */\n  getOptionalStringArray(key: string): string[] | undefined {\n    return this.readConfigValue(key, values => {\n      if (!Array.isArray(values)) {\n        return { expected: 'string-array' };\n      }\n      for (const [index, value] of values.entries()) {\n        if (typeof value !== 'string' || value === '') {\n          return { expected: 'string-array', value, key: `${key}[${index}]` };\n        }\n      }\n      return true;\n    });\n  }\n\n  private fullKey(key: string): string {\n    return `${this.prefix}${this.prefix ? '.' : ''}${key}`;\n  }\n\n  private copy(data: JsonObject, key: string, fallback?: ConfigReader) {\n    const reader = new ConfigReader(\n      data,\n      this.context,\n      fallback,\n      this.fullKey(key),\n    );\n    reader.filteredKeys = this.filteredKeys;\n    return reader;\n  }\n\n  private readConfigValue<T extends JsonValue>(\n    key: string,\n    validate: (\n      value: JsonValue,\n    ) => { expected: string; value?: JsonValue; key?: string } | true,\n  ): T | undefined {\n    const value = this.readValue(key);\n\n    if (value === undefined) {\n      if (process.env.NODE_ENV === 'development') {\n        const fullKey = this.fullKey(key);\n        if (\n          this.filteredKeys?.includes(fullKey) &&\n          !this.notifiedFilteredKeys.has(fullKey)\n        ) {\n          this.notifiedFilteredKeys.add(fullKey);\n          // eslint-disable-next-line no-console\n          console.warn(\n            `Failed to read configuration value at '${fullKey}' as it is not visible. ` +\n              'See https://backstage.io/docs/conf/defining#visibility for instructions on how to make it visible.',\n          );\n        }\n      }\n\n      return this.fallback?.readConfigValue(key, validate);\n    }\n    if (value === null) {\n      return undefined;\n    }\n    const result = validate(value);\n    if (result !== true) {\n      const { key: keyName = key, value: theValue = value, expected } = result;\n      throw new TypeError(\n        errors.type(\n          this.fullKey(keyName),\n          this.context,\n          typeOf(theValue),\n          expected,\n        ),\n      );\n    }\n\n    return value as T;\n  }\n\n  private readValue(key?: string): JsonValue | undefined {\n    const parts = key ? key.split('.') : [];\n    for (const part of parts) {\n      if (!CONFIG_KEY_PART_PATTERN.test(part)) {\n        throw new TypeError(`Invalid config key '${key}'`);\n      }\n    }\n\n    if (this.data === undefined) {\n      return undefined;\n    }\n\n    let value: JsonValue | undefined = this.data;\n    for (const [index, part] of parts.entries()) {\n      if (isObject(value)) {\n        value = value[part];\n      } else if (value !== undefined && value !== null) {\n        const badKey = this.fullKey(parts.slice(0, index).join('.'));\n        throw new TypeError(\n          errors.type(badKey, this.context, typeOf(value), 'object'),\n        );\n      }\n    }\n\n    return value;\n  }\n}\n"], "names": [], "mappings": "AAoBA,MAAM,uBAA0B,GAAA,0CAAA;AAEhC,SAAS,SAAS,KAAmD,EAAA;AACnE,EAAO,OAAA,OAAO,UAAU,QAAY,IAAA,KAAA,KAAU,QAAQ,CAAC,KAAA,CAAM,QAAQ,KAAK,CAAA;AAC5E;AAEA,SAAS,UAAU,KAA4D,EAAA;AAC7E,EAAA,IAAI,OAAO,KAAA,KAAU,QAAY,IAAA,KAAA,KAAU,IAAM,EAAA;AAC/C,IAAO,OAAA,KAAA;AAAA;AAET,EAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AACxB,IAAO,OAAA,KAAA,CAAM,IAAI,SAAS,CAAA;AAAA;AAE5B,EAAA,OAAO,MAAO,CAAA,WAAA;AAAA,IACZ,MAAO,CAAA,OAAA,CAAQ,KAAK,CAAA,CAAE,IAAI,CAAC,CAAC,CAAG,EAAA,CAAC,MAAM,CAAC,CAAA,EAAG,SAAU,CAAA,CAAC,CAAC,CAAC;AAAA,GACzD;AACF;AAEA,SAAS,KAAA,CACP,MACA,IACuB,EAAA;AACvB,EAAA,IAAI,SAAS,IAAM,EAAA;AACjB,IAAO,OAAA,KAAA,CAAA;AAAA;AAET,EAAA,IAAI,SAAS,KAAW,CAAA,EAAA;AACtB,IAAA,OAAO,IAAS,KAAA,KAAA,CAAA,GAAY,KAAY,CAAA,GAAA,KAAA,CAAM,IAAI,CAAA;AAAA;AAEpD,EAAA,IAAI,OAAO,IAAS,KAAA,QAAA,IAAY,KAAM,CAAA,OAAA,CAAQ,IAAI,CAAG,EAAA;AACnD,IAAO,OAAA,IAAA;AAAA;AAET,EAAA,MAAM,OAAU,GAAA,QAAA,CAAS,IAAI,CAAA,GAAI,OAAO,EAAC;AAEzC,EAAA,MAAM,MAAkB,EAAC;AACzB,EAAA,KAAA,MAAW,GAAO,oBAAA,IAAI,GAAI,CAAA,CAAC,GAAG,MAAO,CAAA,IAAA,CAAK,IAAI,CAAA,EAAG,GAAG,MAAO,CAAA,IAAA,CAAK,OAAO,CAAC,CAAC,CAAG,EAAA;AAC1E,IAAA,MAAM,MAAM,KAAM,CAAA,IAAA,CAAK,GAAG,CAAG,EAAA,OAAA,CAAQ,GAAG,CAAC,CAAA;AACzC,IAAA,IAAI,QAAQ,KAAW,CAAA,EAAA;AACrB,MAAA,GAAA,CAAI,GAAG,CAAI,GAAA,GAAA;AAAA;AACb;AAEF,EAAO,OAAA,GAAA;AACT;AAEA,SAAS,OAAO,KAAsC,EAAA;AACpD,EAAA,IAAI,UAAU,IAAM,EAAA;AAClB,IAAO,OAAA,MAAA;AAAA,GACE,MAAA,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AAC/B,IAAO,OAAA,OAAA;AAAA;AAET,EAAA,MAAM,OAAO,OAAO,KAAA;AACpB,EAAA,IAAI,IAAS,KAAA,QAAA,IAAY,KAAM,CAAA,KAAe,CAAG,EAAA;AAC/C,IAAO,OAAA,KAAA;AAAA;AAET,EAAI,IAAA,IAAA,KAAS,QAAY,IAAA,KAAA,KAAU,EAAI,EAAA;AACrC,IAAO,OAAA,cAAA;AAAA;AAET,EAAO,OAAA,IAAA;AACT;AAGA,MAAM,MAAS,GAAA;AAAA,EACb,IAAK,CAAA,GAAA,EAAa,OAAiB,EAAA,QAAA,EAAkB,QAAkB,EAAA;AACrE,IAAA,OAAO,mCAAmC,GAAG,CAAA,MAAA,EAAS,OAAO,CAAU,OAAA,EAAA,QAAQ,YAAY,QAAQ,CAAA,CAAA;AAAA,GACrG;AAAA,EACA,OAAA,CAAQ,KAAa,OAAiB,EAAA;AACpC,IAAO,OAAA,CAAA,kCAAA,EAAqC,GAAG,CAAA,MAAA,EAAS,OAAO,CAAA,CAAA,CAAA;AAAA,GACjE;AAAA,EACA,OAAA,CAAQ,GAAa,EAAA,OAAA,EAAiB,QAAkB,EAAA;AACtD,IAAA,OAAO,CAA2C,wCAAA,EAAA,GAAG,CAAS,MAAA,EAAA,OAAO,UAAU,QAAQ,CAAA,CAAA;AAAA;AAE3F,CAAA;AAQO,MAAM,YAA+B,CAAA;AAAA,EA2C1C,YACmB,IACA,EAAA,OAAA,GAAkB,aAClB,EAAA,QAAA,EACA,SAAiB,EAClC,EAAA;AAJiB,IAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,IAAA,IAAA,CAAA,OAAA,GAAA,OAAA;AACA,IAAA,IAAA,CAAA,QAAA,GAAA,QAAA;AACA,IAAA,IAAA,CAAA,MAAA,GAAA,MAAA;AAAA;AAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAxCK,YAAA;AAAA,EACA,oBAAA,uBAA2B,GAAY,EAAA;AAAA;AAAA;AAAA;AAAA,EAK/C,OAAO,YAAY,OAAoC,EAAA;AACrD,IAAI,IAAA,OAAA,CAAQ,WAAW,CAAG,EAAA;AACxB,MAAO,OAAA,IAAI,aAAa,KAAS,CAAA,CAAA;AAAA;AAKnC,IAAA,OAAO,OAAQ,CAAA,MAAA;AAAA,MACb,CAAC,cAAgB,EAAA,EAAE,MAAM,OAAS,EAAA,YAAA,EAAc,gBAAqB,KAAA;AACnE,QAAA,MAAM,MAAS,GAAA,IAAI,YAAa,CAAA,IAAA,EAAM,SAAS,cAAc,CAAA;AAC7D,QAAA,MAAA,CAAO,YAAe,GAAA,YAAA;AAEtB,QAAA,IAAI,cAAgB,EAAA;AAClB,UAAA,KAAA,MAAW,EAAE,GAAA,EAAK,WAAY,EAAA,IAAK,cAAgB,EAAA;AAEjD,YAAQ,OAAA,CAAA,IAAA;AAAA,cACN,0BAA0B,GAAG,CAAA,KAAA,EAAQ,OAAO,CAAA,wCAAA,EAC1C,eAAe,EACjB,CAAA;AAAA,aACF;AAAA;AACF;AAGF,QAAO,OAAA,MAAA;AAAA,OACT;AAAA,MACA,KAAA;AAAA,KACF;AAAA;AACF;AAAA,EAUA,IAAI,GAAsB,EAAA;AACxB,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,SAAA,CAAU,GAAG,CAAA;AAChC,IAAA,IAAI,UAAU,IAAM,EAAA;AAClB,MAAO,OAAA,KAAA;AAAA;AAET,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAO,OAAA,IAAA;AAAA;AAET,IAAA,OAAO,IAAK,CAAA,QAAA,EAAU,GAAI,CAAA,GAAG,CAAK,IAAA,KAAA;AAAA;AACpC;AAAA,EAGA,IAAiB,GAAA;AACf,IAAM,MAAA,SAAA,GAAY,KAAK,IAAO,GAAA,MAAA,CAAO,KAAK,IAAK,CAAA,IAAI,IAAI,EAAC;AACxD,IAAA,MAAM,YAAe,GAAA,IAAA,CAAK,QAAU,EAAA,IAAA,MAAU,EAAC;AAC/C,IAAO,OAAA,CAAC,mBAAG,IAAI,GAAI,CAAA,CAAC,GAAG,SAAA,EAAW,GAAG,YAAY,CAAC,CAAC,CAAE,CAAA,MAAA;AAAA,MACnD,CAAK,CAAA,KAAA,IAAA,CAAK,IAAO,GAAA,CAAC,CAAM,KAAA;AAAA,KAC1B;AAAA;AACF;AAAA,EAGA,IAAmB,GAAiB,EAAA;AAClC,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,WAAA,CAAY,GAAG,CAAA;AAClC,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAM,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,OAAQ,CAAA,IAAA,CAAK,OAAQ,CAAA,GAAA,IAAO,EAAE,CAAA,EAAG,IAAK,CAAA,OAAO,CAAC,CAAA;AAAA;AAEvE,IAAO,OAAA,KAAA;AAAA;AACT;AAAA,EAGA,YAA2B,GAA6B,EAAA;AACtD,IAAA,MAAM,KAAQ,GAAA,SAAA,CAAU,IAAK,CAAA,SAAA,CAAU,GAAG,CAAC,CAAA;AAC3C,IAAA,MAAM,aAAgB,GAAA,IAAA,CAAK,QAAU,EAAA,WAAA,CAAY,GAAG,CAAA;AAEpD,IAAA,IAAI,UAAU,IAAM,EAAA;AAClB,MAAO,OAAA,KAAA,CAAA;AAAA;AAET,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAI,IAAA,OAAA,CAAQ,GAAI,CAAA,QAAA,KAAa,aAAe,EAAA;AAC1C,QAAI,IAAA,aAAA,KAAkB,UAAa,GAAK,EAAA;AACtC,UAAM,MAAA,OAAA,GAAU,IAAK,CAAA,OAAA,CAAQ,GAAG,CAAA;AAChC,UACE,IAAA,IAAA,CAAK,YAAc,EAAA,QAAA,CAAS,OAAO,CAAA,IACnC,CAAC,IAAK,CAAA,oBAAA,CAAqB,GAAI,CAAA,OAAO,CACtC,EAAA;AACA,YAAK,IAAA,CAAA,oBAAA,CAAqB,IAAI,OAAO,CAAA;AAErC,YAAQ,OAAA,CAAA,IAAA;AAAA,cACN,0CAA0C,OAAO,CAAA,0HAAA;AAAA,aAEnD;AAAA;AACF;AACF;AAEF,MAAA,OAAO,MAAM,aAAa,CAAA;AAAA,KAC5B,MAAA,IAAW,kBAAkB,KAAW,CAAA,EAAA;AACtC,MAAA,OAAO,MAAM,KAAK,CAAA;AAAA;AAGpB,IAAO,OAAA,KAAA,CAAM,OAAO,aAAa,CAAA;AAAA;AACnC;AAAA,EAGA,UAAU,GAA2B,EAAA;AACnC,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,iBAAA,CAAkB,GAAG,CAAA;AACxC,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAM,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,OAAQ,CAAA,IAAA,CAAK,QAAQ,GAAG,CAAA,EAAG,IAAK,CAAA,OAAO,CAAC,CAAA;AAAA;AAEjE,IAAO,OAAA,KAAA;AAAA;AACT;AAAA,EAGA,kBAAkB,GAAuC,EAAA;AACvD,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,SAAA,CAAU,GAAG,CAAA;AAChC,IAAA,MAAM,cAAiB,GAAA,IAAA,CAAK,QAAU,EAAA,iBAAA,CAAkB,GAAG,CAAA;AAE3D,IAAI,IAAA,QAAA,CAAS,KAAK,CAAG,EAAA;AACnB,MAAA,OAAO,IAAK,CAAA,IAAA,CAAK,KAAO,EAAA,GAAA,EAAK,cAAc,CAAA;AAAA;AAE7C,IAAA,IAAI,UAAU,IAAM,EAAA;AAClB,MAAO,OAAA,KAAA,CAAA;AAAA;AAET,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAA,MAAM,IAAI,SAAA;AAAA,QACR,MAAA,CAAO,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,GAAG,CAAG,EAAA,IAAA,CAAK,OAAS,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,QAAQ;AAAA,OACtE;AAAA;AAEF,IAAO,OAAA,cAAA;AAAA;AACT;AAAA,EAGA,eAAe,GAA6B,EAAA;AAC1C,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,sBAAA,CAAuB,GAAG,CAAA;AAC7C,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAM,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,OAAQ,CAAA,IAAA,CAAK,QAAQ,GAAG,CAAA,EAAG,IAAK,CAAA,OAAO,CAAC,CAAA;AAAA;AAEjE,IAAO,OAAA,KAAA;AAAA;AACT;AAAA,EAGA,uBAAuB,GAAyC,EAAA;AAC9D,IAAA,MAAM,OAAU,GAAA,IAAA,CAAK,eAA8B,CAAA,GAAA,EAAK,CAAU,MAAA,KAAA;AAChE,MAAA,IAAI,CAAC,KAAA,CAAM,OAAQ,CAAA,MAAM,CAAG,EAAA;AAC1B,QAAO,OAAA,EAAE,UAAU,cAAe,EAAA;AAAA;AAGpC,MAAA,KAAA,MAAW,CAAC,KAAO,EAAA,KAAK,CAAK,IAAA,MAAA,CAAO,SAAW,EAAA;AAC7C,QAAI,IAAA,CAAC,QAAS,CAAA,KAAK,CAAG,EAAA;AACpB,UAAO,OAAA,EAAE,UAAU,cAAgB,EAAA,KAAA,EAAO,KAAK,CAAG,EAAA,GAAG,CAAI,CAAA,EAAA,KAAK,CAAI,CAAA,CAAA,EAAA;AAAA;AACpE;AAEF,MAAO,OAAA,IAAA;AAAA,KACR,CAAA;AAED,IAAA,IAAI,CAAC,OAAS,EAAA;AACZ,MAAI,IAAA,OAAA,CAAQ,GAAI,CAAA,QAAA,KAAa,aAAe,EAAA;AAC1C,QAAM,MAAA,OAAA,GAAU,IAAK,CAAA,OAAA,CAAQ,GAAG,CAAA;AAChC,QAAA,IACE,IAAK,CAAA,YAAA,EAAc,IAAK,CAAA,CAAA,CAAA,KAAK,EAAE,UAAW,CAAA,OAAO,CAAC,CAAA,IAClD,CAAC,IAAA,CAAK,oBAAqB,CAAA,GAAA,CAAI,GAAG,CAClC,EAAA;AACA,UAAK,IAAA,CAAA,oBAAA,CAAqB,IAAI,GAAG,CAAA;AAEjC,UAAQ,OAAA,CAAA,IAAA;AAAA,YACN,0CAA0C,GAAG,CAAA,8IAAA;AAAA,WAE/C;AAAA;AACF;AAEF,MAAO,OAAA,KAAA,CAAA;AAAA;AAGT,IAAA,OAAO,OAAQ,CAAA,GAAA,CAAI,CAAC,GAAA,EAAK,KAAU,KAAA,IAAA,CAAK,IAAK,CAAA,GAAA,EAAK,CAAG,EAAA,GAAG,CAAI,CAAA,EAAA,KAAK,GAAG,CAAC,CAAA;AAAA;AACvE;AAAA,EAGA,UAAU,GAAqB,EAAA;AAC7B,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,iBAAA,CAAkB,GAAG,CAAA;AACxC,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAM,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,OAAQ,CAAA,IAAA,CAAK,QAAQ,GAAG,CAAA,EAAG,IAAK,CAAA,OAAO,CAAC,CAAA;AAAA;AAEjE,IAAO,OAAA,KAAA;AAAA;AACT;AAAA,EAGA,kBAAkB,GAAiC,EAAA;AACjD,IAAA,MAAM,QAAQ,IAAK,CAAA,eAAA;AAAA,MACjB,GAAA;AAAA,MACA,CAAA,GAAA,KACE,OAAO,GAAQ,KAAA,QAAA,IACf,OAAO,GAAQ,KAAA,QAAA,IAAY,EAAE,QAAA,EAAU,QAAS;AAAA,KACpD;AACA,IAAA,IAAI,OAAO,KAAA,KAAU,QAAY,IAAA,KAAA,KAAU,KAAW,CAAA,EAAA;AACpD,MAAO,OAAA,KAAA;AAAA;AAET,IAAM,MAAA,MAAA,GAAS,OAAO,KAAK,CAAA;AAC3B,IAAA,IAAI,CAAC,MAAA,CAAO,QAAS,CAAA,MAAM,CAAG,EAAA;AAC5B,MAAA,MAAM,IAAI,KAAA;AAAA,QACR,MAAA,CAAO,QAAQ,IAAK,CAAA,OAAA,CAAQ,GAAG,CAAG,EAAA,IAAA,CAAK,SAAS,QAAQ;AAAA,OAC1D;AAAA;AAEF,IAAO,OAAA,MAAA;AAAA;AACT;AAAA,EAGA,WAAW,GAAsB,EAAA;AAC/B,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,kBAAA,CAAmB,GAAG,CAAA;AACzC,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAM,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,OAAQ,CAAA,IAAA,CAAK,QAAQ,GAAG,CAAA,EAAG,IAAK,CAAA,OAAO,CAAC,CAAA;AAAA;AAEjE,IAAO,OAAA,KAAA;AAAA;AACT;AAAA,EAGA,mBAAmB,GAAkC,EAAA;AACnD,IAAA,MAAM,QAAQ,IAAK,CAAA,eAAA;AAAA,MACjB,GAAA;AAAA,MACA,CACE,GAAA,KAAA,OAAO,GAAQ,KAAA,SAAA,IACf,OAAO,GAAA,KAAQ,QACf,IAAA,OAAO,GAAQ,KAAA,QAAA,IAAY,EAAE,QAAA,EAAU,SAAU;AAAA,KACrD;AACA,IAAA,IAAI,OAAO,KAAA,KAAU,SAAa,IAAA,KAAA,KAAU,KAAW,CAAA,EAAA;AACrD,MAAO,OAAA,KAAA;AAAA;AAET,IAAA,MAAM,WAAc,GAAA,MAAA,CAAO,KAAK,CAAA,CAAE,IAAK,EAAA;AAEvC,IAAI,IAAA,wBAAA,CAAyB,IAAK,CAAA,WAAW,CAAG,EAAA;AAC9C,MAAO,OAAA,IAAA;AAAA;AAET,IAAI,IAAA,yBAAA,CAA0B,IAAK,CAAA,WAAW,CAAG,EAAA;AAC/C,MAAO,OAAA,KAAA;AAAA;AAET,IAAM,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,OAAQ,CAAA,IAAA,CAAK,OAAQ,CAAA,GAAG,CAAG,EAAA,IAAA,CAAK,OAAS,EAAA,SAAS,CAAC,CAAA;AAAA;AAC5E;AAAA,EAGA,UAAU,GAAqB,EAAA;AAC7B,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,iBAAA,CAAkB,GAAG,CAAA;AACxC,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAM,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,OAAQ,CAAA,IAAA,CAAK,QAAQ,GAAG,CAAA,EAAG,IAAK,CAAA,OAAO,CAAC,CAAA;AAAA;AAEjE,IAAO,OAAA,KAAA;AAAA;AACT;AAAA,EAGA,kBAAkB,GAAiC,EAAA;AACjD,IAAA,OAAO,IAAK,CAAA,eAAA;AAAA,MACV,GAAA;AAAA,MACA,CAAA,KAAA,KACG,OAAO,KAAU,KAAA,QAAA,IAAY,UAAU,EAAO,IAAA,EAAE,UAAU,QAAS;AAAA,KACxE;AAAA;AACF;AAAA,EAGA,eAAe,GAAuB,EAAA;AACpC,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,sBAAA,CAAuB,GAAG,CAAA;AAC7C,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAM,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,OAAQ,CAAA,IAAA,CAAK,QAAQ,GAAG,CAAA,EAAG,IAAK,CAAA,OAAO,CAAC,CAAA;AAAA;AAEjE,IAAO,OAAA,KAAA;AAAA;AACT;AAAA,EAGA,uBAAuB,GAAmC,EAAA;AACxD,IAAO,OAAA,IAAA,CAAK,eAAgB,CAAA,GAAA,EAAK,CAAU,MAAA,KAAA;AACzC,MAAA,IAAI,CAAC,KAAA,CAAM,OAAQ,CAAA,MAAM,CAAG,EAAA;AAC1B,QAAO,OAAA,EAAE,UAAU,cAAe,EAAA;AAAA;AAEpC,MAAA,KAAA,MAAW,CAAC,KAAO,EAAA,KAAK,CAAK,IAAA,MAAA,CAAO,SAAW,EAAA;AAC7C,QAAA,IAAI,OAAO,KAAA,KAAU,QAAY,IAAA,KAAA,KAAU,EAAI,EAAA;AAC7C,UAAO,OAAA,EAAE,UAAU,cAAgB,EAAA,KAAA,EAAO,KAAK,CAAG,EAAA,GAAG,CAAI,CAAA,EAAA,KAAK,CAAI,CAAA,CAAA,EAAA;AAAA;AACpE;AAEF,MAAO,OAAA,IAAA;AAAA,KACR,CAAA;AAAA;AACH,EAEQ,QAAQ,GAAqB,EAAA;AACnC,IAAO,OAAA,CAAA,EAAG,KAAK,MAAM,CAAA,EAAG,KAAK,MAAS,GAAA,GAAA,GAAM,EAAE,CAAA,EAAG,GAAG,CAAA,CAAA;AAAA;AACtD,EAEQ,IAAA,CAAK,IAAkB,EAAA,GAAA,EAAa,QAAyB,EAAA;AACnE,IAAA,MAAM,SAAS,IAAI,YAAA;AAAA,MACjB,IAAA;AAAA,MACA,IAAK,CAAA,OAAA;AAAA,MACL,QAAA;AAAA,MACA,IAAA,CAAK,QAAQ,GAAG;AAAA,KAClB;AACA,IAAA,MAAA,CAAO,eAAe,IAAK,CAAA,YAAA;AAC3B,IAAO,OAAA,MAAA;AAAA;AACT,EAEQ,eAAA,CACN,KACA,QAGe,EAAA;AACf,IAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,SAAA,CAAU,GAAG,CAAA;AAEhC,IAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,MAAI,IAAA,OAAA,CAAQ,GAAI,CAAA,QAAA,KAAa,aAAe,EAAA;AAC1C,QAAM,MAAA,OAAA,GAAU,IAAK,CAAA,OAAA,CAAQ,GAAG,CAAA;AAChC,QACE,IAAA,IAAA,CAAK,YAAc,EAAA,QAAA,CAAS,OAAO,CAAA,IACnC,CAAC,IAAK,CAAA,oBAAA,CAAqB,GAAI,CAAA,OAAO,CACtC,EAAA;AACA,UAAK,IAAA,CAAA,oBAAA,CAAqB,IAAI,OAAO,CAAA;AAErC,UAAQ,OAAA,CAAA,IAAA;AAAA,YACN,0CAA0C,OAAO,CAAA,0HAAA;AAAA,WAEnD;AAAA;AACF;AAGF,MAAA,OAAO,IAAK,CAAA,QAAA,EAAU,eAAgB,CAAA,GAAA,EAAK,QAAQ,CAAA;AAAA;AAErD,IAAA,IAAI,UAAU,IAAM,EAAA;AAClB,MAAO,OAAA,KAAA,CAAA;AAAA;AAET,IAAM,MAAA,MAAA,GAAS,SAAS,KAAK,CAAA;AAC7B,IAAA,IAAI,WAAW,IAAM,EAAA;AACnB,MAAM,MAAA,EAAE,KAAK,OAAU,GAAA,GAAA,EAAK,OAAO,QAAW,GAAA,KAAA,EAAO,UAAa,GAAA,MAAA;AAClE,MAAA,MAAM,IAAI,SAAA;AAAA,QACR,MAAO,CAAA,IAAA;AAAA,UACL,IAAA,CAAK,QAAQ,OAAO,CAAA;AAAA,UACpB,IAAK,CAAA,OAAA;AAAA,UACL,OAAO,QAAQ,CAAA;AAAA,UACf;AAAA;AACF,OACF;AAAA;AAGF,IAAO,OAAA,KAAA;AAAA;AACT,EAEQ,UAAU,GAAqC,EAAA;AACrD,IAAA,MAAM,QAAQ,GAAM,GAAA,GAAA,CAAI,KAAM,CAAA,GAAG,IAAI,EAAC;AACtC,IAAA,KAAA,MAAW,QAAQ,KAAO,EAAA;AACxB,MAAA,IAAI,CAAC,uBAAA,CAAwB,IAAK,CAAA,IAAI,CAAG,EAAA;AACvC,QAAA,MAAM,IAAI,SAAA,CAAU,CAAuB,oBAAA,EAAA,GAAG,CAAG,CAAA,CAAA,CAAA;AAAA;AACnD;AAGF,IAAI,IAAA,IAAA,CAAK,SAAS,KAAW,CAAA,EAAA;AAC3B,MAAO,OAAA,KAAA,CAAA;AAAA;AAGT,IAAA,IAAI,QAA+B,IAAK,CAAA,IAAA;AACxC,IAAA,KAAA,MAAW,CAAC,KAAO,EAAA,IAAI,CAAK,IAAA,KAAA,CAAM,SAAW,EAAA;AAC3C,MAAI,IAAA,QAAA,CAAS,KAAK,CAAG,EAAA;AACnB,QAAA,KAAA,GAAQ,MAAM,IAAI,CAAA;AAAA,OACT,MAAA,IAAA,KAAA,KAAU,KAAa,CAAA,IAAA,KAAA,KAAU,IAAM,EAAA;AAChD,QAAM,MAAA,MAAA,GAAS,IAAK,CAAA,OAAA,CAAQ,KAAM,CAAA,KAAA,CAAM,GAAG,KAAK,CAAA,CAAE,IAAK,CAAA,GAAG,CAAC,CAAA;AAC3D,QAAA,MAAM,IAAI,SAAA;AAAA,UACR,MAAA,CAAO,KAAK,MAAQ,EAAA,IAAA,CAAK,SAAS,MAAO,CAAA,KAAK,GAAG,QAAQ;AAAA,SAC3D;AAAA;AACF;AAGF,IAAO,OAAA,KAAA;AAAA;AAEX;;;;"}
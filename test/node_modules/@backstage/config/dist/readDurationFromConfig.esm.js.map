{"version": 3, "file": "readDurationFromConfig.esm.js", "sources": ["../src/readDurationFromConfig.ts"], "sourcesContent": ["/*\n * Copyright 2023 The Backstage Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Config } from '@backstage/config';\nimport { InputError, stringifyError } from '@backstage/errors';\nimport { HumanDuration } from '@backstage/types';\nimport ms from 'ms';\n\nexport const propsOfHumanDuration = [\n  'years',\n  'months',\n  'weeks',\n  'days',\n  'hours',\n  'minutes',\n  'seconds',\n  'milliseconds',\n];\n\n/**\n * Reads a duration from config.\n *\n * @public\n * @remarks\n *\n * The supported formats are:\n *\n * - A string in the format of '1d', '2 seconds' etc. as supported by the `ms`\n *   library.\n * - A standard ISO formatted duration string, e.g. 'P2DT6H' or 'PT1M'.\n * - An object with individual units (in plural) as keys, e.g. `{ days: 2, hours: 6 }`.\n *\n * The string forms are naturally only supported if the `options.key` argument\n * is passed, since a `Config` argument always represents an object by its\n * nature.\n *\n * This does not support optionality; if you want to support optional durations,\n * you need to first check the presence of the target with `config.has(...)` and\n * then call this function.\n *\n * @param config - A configuration object\n * @param key - If specified, read the duration from the given subkey under the\n *        config object\n * @returns A duration object\n */\nexport function readDurationFromConfig(\n  config: Config,\n  options?: {\n    key?: string;\n  },\n): HumanDuration {\n  if (options?.key && typeof config.getOptional(options.key) === 'string') {\n    const value = config.getString(options.key).trim();\n    try {\n      return value.startsWith('P')\n        ? parseIsoDuration(value)\n        : parseMsDuration(value);\n    } catch (error) {\n      throw new InputError(\n        `Invalid duration '${value}' in config at '${\n          options.key\n        }', ${stringifyError(error)}`,\n      );\n    }\n  }\n\n  return parseObjectDuration(config, options);\n}\n\n/**\n * Parses the object form of durations.\n */\nexport function parseObjectDuration(\n  config: Config,\n  options?: {\n    key?: string;\n  },\n): HumanDuration {\n  let root: Config;\n  let found = false;\n  const result: Record<string, number> = {};\n\n  try {\n    root = options?.key ? config.getConfig(options.key) : config;\n    for (const prop of propsOfHumanDuration) {\n      const value = root.getOptionalNumber(prop);\n      if (value !== undefined) {\n        result[prop] = value;\n        found = true;\n      }\n    }\n  } catch (error) {\n    // This needs no contextual key prefix since the config reader adds it to\n    // its own errors\n    throw new InputError(`Failed to read duration from config, ${error}`);\n  }\n\n  try {\n    if (!found) {\n      const good = propsOfHumanDuration.map(p => `'${p}'`).join(', ');\n      throw new Error(`Needs one or more of ${good}`);\n    }\n\n    const invalidProps = root\n      .keys()\n      .filter(prop => !propsOfHumanDuration.includes(prop));\n    if (invalidProps.length) {\n      const what = invalidProps.length === 1 ? 'property' : 'properties';\n      const bad = invalidProps.map(p => `'${p}'`).join(', ');\n      const good = propsOfHumanDuration.map(p => `'${p}'`).join(', ');\n      throw new Error(\n        `Unknown ${what} ${bad}; expected one or more of ${good}`,\n      );\n    }\n  } catch (error) {\n    // For our own errors, even though we don't know where we are anchored in\n    // the config hierarchy, we try to be helpful and at least add the subkey if\n    // we have it\n    let prefix = 'Failed to read duration from config';\n    if (options?.key) {\n      prefix += ` at '${options.key}'`;\n    }\n    throw new Error(`${prefix}, ${error}`);\n  }\n\n  return result as HumanDuration;\n}\n\n/**\n * Parses friendly string durations like '1d', '2 seconds' etc using the ms\n * library.\n */\nexport function parseMsDuration(input: string): HumanDuration {\n  if (/^\\d+$/.exec(input)) {\n    // We explicitly disallow the only-digits form of the ms library, because\n    // from a configuration perspective it's just confusing to even be able to\n    // specify that\n    throw new Error(\n      `The value cannot be a plain number; try adding a unit like 'ms' or 'seconds'`,\n    );\n  }\n\n  let milliseconds = ms(input);\n  if (!Number.isFinite(milliseconds)) {\n    throw new Error(\n      `Not a valid duration string, try a number followed by a unit such as '1d' or '2 seconds'`,\n    );\n  } else if (milliseconds < 0) {\n    throw new Error('Negative durations are not allowed');\n  } else if (milliseconds === 0) {\n    return { milliseconds: 0 };\n  }\n\n  // As used by the ms library\n  const s = 1000;\n  const m = s * 60;\n  const h = m * 60;\n  const d = h * 24;\n  const w = d * 7;\n  const y = d * 365.25;\n\n  const result: HumanDuration = {};\n\n  if (milliseconds >= y) {\n    const years = Math.floor(milliseconds / y);\n    milliseconds -= years * y;\n    result.years = years;\n  }\n\n  if (milliseconds >= w) {\n    const weeks = Math.floor(milliseconds / w);\n    milliseconds -= weeks * w;\n    result.weeks = weeks;\n  }\n\n  if (milliseconds >= d) {\n    const days = Math.floor(milliseconds / d);\n    milliseconds -= days * d;\n    result.days = days;\n  }\n\n  if (milliseconds >= h) {\n    const hours = Math.floor(milliseconds / h);\n    milliseconds -= hours * h;\n    result.hours = hours;\n  }\n\n  if (milliseconds >= m) {\n    const minutes = Math.floor(milliseconds / m);\n    milliseconds -= minutes * m;\n    result.minutes = minutes;\n  }\n\n  if (milliseconds >= s) {\n    const seconds = Math.floor(milliseconds / s);\n    milliseconds -= seconds * s;\n    result.seconds = seconds;\n  }\n\n  if (milliseconds > 0) {\n    result.milliseconds = milliseconds;\n  }\n\n  return result;\n}\n\n/**\n * Parses an ISO formatted duration string.\n *\n * Implementation taken from luxon's Duration.fromISO to not force that\n * dependency on everyone.\n */\nexport function parseIsoDuration(input: string): HumanDuration {\n  const match =\n    /^-?P(?:(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)Y)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)W)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)D)?(?:T(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)H)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20})(?:[.,](-?\\d{1,20}))?S)?)?)$/.exec(\n      input,\n    );\n  if (!match) {\n    throw new Error(\n      `Invalid ISO format, expected a value similar to 'P2DT6H' (2 days 6 hours) or 'PT1M' (1 minute)`,\n    );\n  }\n\n  const [\n    s,\n    yearStr,\n    monthStr,\n    weekStr,\n    dayStr,\n    hourStr,\n    minuteStr,\n    secondStr,\n    millisecondsStr,\n  ] = match;\n\n  const hasNegativePrefix = s[0] === '-';\n  const negativeSeconds = !!secondStr && secondStr[0] === '-';\n\n  const maybeNegate = (num: number | undefined, force = false) =>\n    num !== undefined && (force || (num && hasNegativePrefix)) ? -num : num;\n\n  const parseFloating = (value: string) => {\n    if (typeof value === 'undefined' || value === null || value === '') {\n      return undefined;\n    }\n    return parseFloat(value);\n  };\n\n  const parseMillis = (fraction: string | undefined) => {\n    // Return undefined (instead of 0) in these cases, where fraction is not set\n    if (\n      typeof fraction === 'undefined' ||\n      fraction === null ||\n      fraction === ''\n    ) {\n      return undefined;\n    }\n    const f = parseFloat(`0.${fraction}`) * 1000;\n    return Math.floor(f);\n  };\n\n  const years = maybeNegate(parseFloating(yearStr));\n  const months = maybeNegate(parseFloating(monthStr));\n  const weeks = maybeNegate(parseFloating(weekStr));\n  const days = maybeNegate(parseFloating(dayStr));\n  const hours = maybeNegate(parseFloating(hourStr));\n  const minutes = maybeNegate(parseFloating(minuteStr));\n  const seconds = maybeNegate(parseFloating(secondStr), secondStr === '-0');\n  const milliseconds = maybeNegate(\n    parseMillis(millisecondsStr),\n    negativeSeconds,\n  );\n\n  if (\n    years === undefined &&\n    months === undefined &&\n    weeks === undefined &&\n    days === undefined &&\n    hours === undefined &&\n    minutes === undefined &&\n    seconds === undefined &&\n    milliseconds === undefined\n  ) {\n    throw new Error('Invalid ISO format, no values given');\n  }\n\n  return {\n    ...(years ? { years } : {}),\n    ...(months ? { months } : {}),\n    ...(weeks ? { weeks } : {}),\n    ...(days ? { days } : {}),\n    ...(hours ? { hours } : {}),\n    ...(minutes ? { minutes } : {}),\n    ...(seconds ? { seconds } : {}),\n    ...(milliseconds ? { milliseconds } : {}),\n  };\n}\n"], "names": [], "mappings": ";;;AAqBO,MAAM,oBAAuB,GAAA;AAAA,EAClC,OAAA;AAAA,EACA,QAAA;AAAA,EACA,OAAA;AAAA,EACA,MAAA;AAAA,EACA,OAAA;AAAA,EACA,SAAA;AAAA,EACA,SAAA;AAAA,EACA;AACF;AA4BgB,SAAA,sBAAA,CACd,QACA,OAGe,EAAA;AACf,EAAI,IAAA,OAAA,EAAS,OAAO,OAAO,MAAA,CAAO,YAAY,OAAQ,CAAA,GAAG,MAAM,QAAU,EAAA;AACvE,IAAA,MAAM,QAAQ,MAAO,CAAA,SAAA,CAAU,OAAQ,CAAA,GAAG,EAAE,IAAK,EAAA;AACjD,IAAI,IAAA;AACF,MAAO,OAAA,KAAA,CAAM,WAAW,GAAG,CAAA,GACvB,iBAAiB,KAAK,CAAA,GACtB,gBAAgB,KAAK,CAAA;AAAA,aAClB,KAAO,EAAA;AACd,MAAA,MAAM,IAAI,UAAA;AAAA,QACR,CAAA,kBAAA,EAAqB,KAAK,CACxB,gBAAA,EAAA,OAAA,CAAQ,GACV,CAAM,GAAA,EAAA,cAAA,CAAe,KAAK,CAAC,CAAA;AAAA,OAC7B;AAAA;AACF;AAGF,EAAO,OAAA,mBAAA,CAAoB,QAAQ,OAAO,CAAA;AAC5C;AAKgB,SAAA,mBAAA,CACd,QACA,OAGe,EAAA;AACf,EAAI,IAAA,IAAA;AACJ,EAAA,IAAI,KAAQ,GAAA,KAAA;AACZ,EAAA,MAAM,SAAiC,EAAC;AAExC,EAAI,IAAA;AACF,IAAA,IAAA,GAAO,SAAS,GAAM,GAAA,MAAA,CAAO,SAAU,CAAA,OAAA,CAAQ,GAAG,CAAI,GAAA,MAAA;AACtD,IAAA,KAAA,MAAW,QAAQ,oBAAsB,EAAA;AACvC,MAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,iBAAA,CAAkB,IAAI,CAAA;AACzC,MAAA,IAAI,UAAU,KAAW,CAAA,EAAA;AACvB,QAAA,MAAA,CAAO,IAAI,CAAI,GAAA,KAAA;AACf,QAAQ,KAAA,GAAA,IAAA;AAAA;AACV;AACF,WACO,KAAO,EAAA;AAGd,IAAA,MAAM,IAAI,UAAA,CAAW,CAAwC,qCAAA,EAAA,KAAK,CAAE,CAAA,CAAA;AAAA;AAGtE,EAAI,IAAA;AACF,IAAA,IAAI,CAAC,KAAO,EAAA;AACV,MAAM,MAAA,IAAA,GAAO,qBAAqB,GAAI,CAAA,CAAA,CAAA,KAAK,IAAI,CAAC,CAAA,CAAA,CAAG,CAAE,CAAA,IAAA,CAAK,IAAI,CAAA;AAC9D,MAAA,MAAM,IAAI,KAAA,CAAM,CAAwB,qBAAA,EAAA,IAAI,CAAE,CAAA,CAAA;AAAA;AAGhD,IAAM,MAAA,YAAA,GAAe,IAClB,CAAA,IAAA,EACA,CAAA,MAAA,CAAO,UAAQ,CAAC,oBAAA,CAAqB,QAAS,CAAA,IAAI,CAAC,CAAA;AACtD,IAAA,IAAI,aAAa,MAAQ,EAAA;AACvB,MAAA,MAAM,IAAO,GAAA,YAAA,CAAa,MAAW,KAAA,CAAA,GAAI,UAAa,GAAA,YAAA;AACtD,MAAM,MAAA,GAAA,GAAM,aAAa,GAAI,CAAA,CAAA,CAAA,KAAK,IAAI,CAAC,CAAA,CAAA,CAAG,CAAE,CAAA,IAAA,CAAK,IAAI,CAAA;AACrD,MAAM,MAAA,IAAA,GAAO,qBAAqB,GAAI,CAAA,CAAA,CAAA,KAAK,IAAI,CAAC,CAAA,CAAA,CAAG,CAAE,CAAA,IAAA,CAAK,IAAI,CAAA;AAC9D,MAAA,MAAM,IAAI,KAAA;AAAA,QACR,CAAW,QAAA,EAAA,IAAI,CAAI,CAAA,EAAA,GAAG,6BAA6B,IAAI,CAAA;AAAA,OACzD;AAAA;AACF,WACO,KAAO,EAAA;AAId,IAAA,IAAI,MAAS,GAAA,qCAAA;AACb,IAAA,IAAI,SAAS,GAAK,EAAA;AAChB,MAAU,MAAA,IAAA,CAAA,KAAA,EAAQ,QAAQ,GAAG,CAAA,CAAA,CAAA;AAAA;AAE/B,IAAA,MAAM,IAAI,KAAM,CAAA,CAAA,EAAG,MAAM,CAAA,EAAA,EAAK,KAAK,CAAE,CAAA,CAAA;AAAA;AAGvC,EAAO,OAAA,MAAA;AACT;AAMO,SAAS,gBAAgB,KAA8B,EAAA;AAC5D,EAAI,IAAA,OAAA,CAAQ,IAAK,CAAA,KAAK,CAAG,EAAA;AAIvB,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,CAAA,4EAAA;AAAA,KACF;AAAA;AAGF,EAAI,IAAA,YAAA,GAAe,GAAG,KAAK,CAAA;AAC3B,EAAA,IAAI,CAAC,MAAA,CAAO,QAAS,CAAA,YAAY,CAAG,EAAA;AAClC,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,CAAA,wFAAA;AAAA,KACF;AAAA,GACF,MAAA,IAAW,eAAe,CAAG,EAAA;AAC3B,IAAM,MAAA,IAAI,MAAM,oCAAoC,CAAA;AAAA,GACtD,MAAA,IAAW,iBAAiB,CAAG,EAAA;AAC7B,IAAO,OAAA,EAAE,cAAc,CAAE,EAAA;AAAA;AAI3B,EAAA,MAAM,CAAI,GAAA,GAAA;AACV,EAAA,MAAM,IAAI,CAAI,GAAA,EAAA;AACd,EAAA,MAAM,IAAI,CAAI,GAAA,EAAA;AACd,EAAA,MAAM,IAAI,CAAI,GAAA,EAAA;AACd,EAAA,MAAM,IAAI,CAAI,GAAA,CAAA;AACd,EAAA,MAAM,IAAI,CAAI,GAAA,MAAA;AAEd,EAAA,MAAM,SAAwB,EAAC;AAE/B,EAAA,IAAI,gBAAgB,CAAG,EAAA;AACrB,IAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,KAAM,CAAA,YAAA,GAAe,CAAC,CAAA;AACzC,IAAA,YAAA,IAAgB,KAAQ,GAAA,CAAA;AACxB,IAAA,MAAA,CAAO,KAAQ,GAAA,KAAA;AAAA;AAGjB,EAAA,IAAI,gBAAgB,CAAG,EAAA;AACrB,IAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,KAAM,CAAA,YAAA,GAAe,CAAC,CAAA;AACzC,IAAA,YAAA,IAAgB,KAAQ,GAAA,CAAA;AACxB,IAAA,MAAA,CAAO,KAAQ,GAAA,KAAA;AAAA;AAGjB,EAAA,IAAI,gBAAgB,CAAG,EAAA;AACrB,IAAA,MAAM,IAAO,GAAA,IAAA,CAAK,KAAM,CAAA,YAAA,GAAe,CAAC,CAAA;AACxC,IAAA,YAAA,IAAgB,IAAO,GAAA,CAAA;AACvB,IAAA,MAAA,CAAO,IAAO,GAAA,IAAA;AAAA;AAGhB,EAAA,IAAI,gBAAgB,CAAG,EAAA;AACrB,IAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,KAAM,CAAA,YAAA,GAAe,CAAC,CAAA;AACzC,IAAA,YAAA,IAAgB,KAAQ,GAAA,CAAA;AACxB,IAAA,MAAA,CAAO,KAAQ,GAAA,KAAA;AAAA;AAGjB,EAAA,IAAI,gBAAgB,CAAG,EAAA;AACrB,IAAA,MAAM,OAAU,GAAA,IAAA,CAAK,KAAM,CAAA,YAAA,GAAe,CAAC,CAAA;AAC3C,IAAA,YAAA,IAAgB,OAAU,GAAA,CAAA;AAC1B,IAAA,MAAA,CAAO,OAAU,GAAA,OAAA;AAAA;AAGnB,EAAA,IAAI,gBAAgB,CAAG,EAAA;AACrB,IAAA,MAAM,OAAU,GAAA,IAAA,CAAK,KAAM,CAAA,YAAA,GAAe,CAAC,CAAA;AAC3C,IAAA,YAAA,IAAgB,OAAU,GAAA,CAAA;AAC1B,IAAA,MAAA,CAAO,OAAU,GAAA,OAAA;AAAA;AAGnB,EAAA,IAAI,eAAe,CAAG,EAAA;AACpB,IAAA,MAAA,CAAO,YAAe,GAAA,YAAA;AAAA;AAGxB,EAAO,OAAA,MAAA;AACT;AAQO,SAAS,iBAAiB,KAA8B,EAAA;AAC7D,EAAA,MAAM,QACJ,8PAA+P,CAAA,IAAA;AAAA,IAC7P;AAAA,GACF;AACF,EAAA,IAAI,CAAC,KAAO,EAAA;AACV,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,CAAA,8FAAA;AAAA,KACF;AAAA;AAGF,EAAM,MAAA;AAAA,IACJ,CAAA;AAAA,IACA,OAAA;AAAA,IACA,QAAA;AAAA,IACA,OAAA;AAAA,IACA,MAAA;AAAA,IACA,OAAA;AAAA,IACA,SAAA;AAAA,IACA,SAAA;AAAA,IACA;AAAA,GACE,GAAA,KAAA;AAEJ,EAAM,MAAA,iBAAA,GAAoB,CAAE,CAAA,CAAC,CAAM,KAAA,GAAA;AACnC,EAAA,MAAM,kBAAkB,CAAC,CAAC,SAAa,IAAA,SAAA,CAAU,CAAC,CAAM,KAAA,GAAA;AAExD,EAAM,MAAA,WAAA,GAAc,CAAC,GAAA,EAAyB,KAAQ,GAAA,KAAA,KACpD,GAAQ,KAAA,KAAA,CAAA,KAAc,KAAU,IAAA,GAAA,IAAO,iBAAsB,CAAA,GAAA,CAAC,GAAM,GAAA,GAAA;AAEtE,EAAM,MAAA,aAAA,GAAgB,CAAC,KAAkB,KAAA;AACvC,IAAA,IAAI,OAAO,KAAU,KAAA,WAAA,IAAe,KAAU,KAAA,IAAA,IAAQ,UAAU,EAAI,EAAA;AAClE,MAAO,OAAA,KAAA,CAAA;AAAA;AAET,IAAA,OAAO,WAAW,KAAK,CAAA;AAAA,GACzB;AAEA,EAAM,MAAA,WAAA,GAAc,CAAC,QAAiC,KAAA;AAEpD,IAAA,IACE,OAAO,QAAa,KAAA,WAAA,IACpB,QAAa,KAAA,IAAA,IACb,aAAa,EACb,EAAA;AACA,MAAO,OAAA,KAAA,CAAA;AAAA;AAET,IAAA,MAAM,CAAI,GAAA,UAAA,CAAW,CAAK,EAAA,EAAA,QAAQ,EAAE,CAAI,GAAA,GAAA;AACxC,IAAO,OAAA,IAAA,CAAK,MAAM,CAAC,CAAA;AAAA,GACrB;AAEA,EAAA,MAAM,KAAQ,GAAA,WAAA,CAAY,aAAc,CAAA,OAAO,CAAC,CAAA;AAChD,EAAA,MAAM,MAAS,GAAA,WAAA,CAAY,aAAc,CAAA,QAAQ,CAAC,CAAA;AAClD,EAAA,MAAM,KAAQ,GAAA,WAAA,CAAY,aAAc,CAAA,OAAO,CAAC,CAAA;AAChD,EAAA,MAAM,IAAO,GAAA,WAAA,CAAY,aAAc,CAAA,MAAM,CAAC,CAAA;AAC9C,EAAA,MAAM,KAAQ,GAAA,WAAA,CAAY,aAAc,CAAA,OAAO,CAAC,CAAA;AAChD,EAAA,MAAM,OAAU,GAAA,WAAA,CAAY,aAAc,CAAA,SAAS,CAAC,CAAA;AACpD,EAAA,MAAM,UAAU,WAAY,CAAA,aAAA,CAAc,SAAS,CAAA,EAAG,cAAc,IAAI,CAAA;AACxE,EAAA,MAAM,YAAe,GAAA,WAAA;AAAA,IACnB,YAAY,eAAe,CAAA;AAAA,IAC3B;AAAA,GACF;AAEA,EAAA,IACE,KAAU,KAAA,KAAA,CAAA,IACV,MAAW,KAAA,KAAA,CAAA,IACX,UAAU,KACV,CAAA,IAAA,IAAA,KAAS,KACT,CAAA,IAAA,KAAA,KAAU,UACV,OAAY,KAAA,KAAA,CAAA,IACZ,OAAY,KAAA,KAAA,CAAA,IACZ,iBAAiB,KACjB,CAAA,EAAA;AACA,IAAM,MAAA,IAAI,MAAM,qCAAqC,CAAA;AAAA;AAGvD,EAAO,OAAA;AAAA,IACL,GAAI,KAAA,GAAQ,EAAE,KAAA,KAAU,EAAC;AAAA,IACzB,GAAI,MAAA,GAAS,EAAE,MAAA,KAAW,EAAC;AAAA,IAC3B,GAAI,KAAA,GAAQ,EAAE,KAAA,KAAU,EAAC;AAAA,IACzB,GAAI,IAAA,GAAO,EAAE,IAAA,KAAS,EAAC;AAAA,IACvB,GAAI,KAAA,GAAQ,EAAE,KAAA,KAAU,EAAC;AAAA,IACzB,GAAI,OAAA,GAAU,EAAE,OAAA,KAAY,EAAC;AAAA,IAC7B,GAAI,OAAA,GAAU,EAAE,OAAA,KAAY,EAAC;AAAA,IAC7B,GAAI,YAAA,GAAe,EAAE,YAAA,KAAiB;AAAC,GACzC;AACF;;;;"}
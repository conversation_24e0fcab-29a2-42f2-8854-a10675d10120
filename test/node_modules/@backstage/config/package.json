{"name": "@backstage/config", "version": "1.3.2", "description": "Config API used by Backstage core, backend, and CLI", "backstage": {"role": "common-library"}, "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts"}, "keywords": ["backstage"], "homepage": "https://backstage.io", "repository": {"type": "git", "url": "https://github.com/backstage/backstage", "directory": "packages/config"}, "license": "Apache-2.0", "sideEffects": false, "main": "dist/index.cjs.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "backstage-cli package build", "clean": "backstage-cli package clean", "lint": "backstage-cli package lint", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack", "test": "backstage-cli package test"}, "dependencies": {"@backstage/errors": "^1.2.7", "@backstage/types": "^1.2.1", "ms": "^2.1.3"}, "devDependencies": {"@backstage/cli": "^0.29.5", "@backstage/test-utils": "^1.7.4"}, "typesVersions": {"*": {"index": ["dist/index.d.ts"]}}, "module": "dist/index.esm.js"}
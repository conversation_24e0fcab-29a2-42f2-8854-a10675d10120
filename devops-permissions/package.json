{"name": "backstage-permission-service", "description": "Serviço de Permissinamento de DEVOPS (Backstage.io)", "version": "1.0.0", "nodeVersion": "20.3.1", "main": "dist/index.js", "scripts": {"start": "ts-node src/index.ts", "dev": "ts-node src/index.ts --watch", "build": "rm -rf dist; tsc -p .; cp src/permissionData.json dist/permissionData.json", "test": "jest", "test:watch": "jest --watch", "commit": "git add . && git-cz && git push"}, "dependencies": {"@backstage/backend-common": "^0.25.0", "@backstage/catalog-model": "^1.7.4", "@backstage/plugin-auth-node": "^0.6.3", "@backstage/plugin-catalog-backend": "^2.0.0", "@backstage/plugin-catalog-common": "^1.1.4", "@backstage/plugin-permission-common": "^0.9.0", "@backstage/plugin-permission-node": "^0.10.0", "body-parser": "^2.2.0", "dotenv": "^16.5.0", "express": "^4.21.2", "git-cz": "^4.9.0", "zod": "^3.25.7"}, "devDependencies": {"@types/express": "^4.17.22", "@types/jest": "^29.5.14", "@types/node": "^22.15.20", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
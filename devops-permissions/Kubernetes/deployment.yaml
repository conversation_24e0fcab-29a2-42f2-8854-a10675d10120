---
#DEPLOYMENT CONFIG
apiVersion: apps.openshift.io/v1
kind: DeploymentConfig
metadata:
  name: __APP_NAME__
  namespace: __APP_NAMESPACE__
  annotation:
    haproxy.router.openshift.io/disable_cookies: 'true'
spec:
  selector:
    app: __APP_NAME__
  replicas: 1
  template:
    metadata:
      labels:
        app: __APP_NAME__
    spec:
      containers:
        - name: __APP_NAME__
          image: __DOCKER_SERVER__/app-images/__DOCKER_IMAGE__:__branchName__-__buildNumber__
          ports:
            - containerPort: 3000
              protocol: TCP
          env:
            - name: AUTH_USER
              valueFrom:
                secretKeyRef:
                  name: backstage-permissions
                  key: AUTH_USER
            - name: AUTH_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: backstage-permissions
                  key: AUTH_PASSWORD            
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: backstage-permissions
                  key: JWT_SECRET
          resources:
            limits:
              cpu: 750m
              memory: 256Mi
            requests:
              cpu: 30m
              memory: 100Mi
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 3000
            periodSeconds: 10
          volumeMounts:
            - name: app-config
              mountPath: /etc/app-config
              readOnly: true
      volumes:
        - name: app-config
          configMap:
            name: config
---
# SERVICE
apiVersion: v1
kind: Service
metadata:
  name: __APP_NAME__
  namespace: __APP_NAMESPACE__
spec:
  ports:
    - name: 80-pp-web-tcp
      port: 80
      targetPort: 8080
      protocol: TCP
  selector:
    app: __APP_NAME__
  type: NodePort

---
# ROUTE
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  name: __APP_NAME__
  namespace: __APP_NAMESPACE__
spec:
  host: __HOSTNAME__
  port:
    targetPort: 80-pp-web-tcp
  to:
    kind: Service
    name: __APP_NAME__
    weight: 100
  wildcardPolicy: None
  tls:
    insecureEdgeTerminationPolicy: Redirect
    termination: edge
import { PolicyQueryUser, PolicyDecision, AuthorizeResult, PolicyQueryPermission } from "./types";
import { catalogConditions, createCatalogConditionalDecision } from "@backstage/plugin-catalog-backend/alpha";
import { isPermission, isResourcePermission } from "@backstage/plugin-permission-common";
import { catalogEntityCreatePermission, catalogEntityReadPermission } from "@backstage/plugin-catalog-common/alpha";
import { permissionData, isTypeServiceTemplate } from "./utils";

export class PermissionPolicy {
  async handle(permission: PolicyQueryPermission, user?: PolicyQueryUser, resource?: any): Promise<PolicyDecision> {
    if (!user || !user.identity) {
      return this.handleAnonymousUser(permission, resource);
    }

    if (permissionData.adminGroups.some((group) => this.isInGroup(group, user))) {
      return { result: AuthorizeResult.ALLOW };
    }

    if (permissionData.denyCatalogCreate && isPermission(permission, catalogEntityCreatePermission)) {
      return { result: AuthorizeResult.DENY };
    }

    if (isPermission(permission, catalogEntityReadPermission)) {
      const userGroupPermission = permissionData.groupPermissions.find((groupPerm) => groupPerm.groups.some((group) => this.isInGroup(group, user)));

      if (userGroupPermission) {
        const resourceType = resource?.spec?.type;
        if (resourceType && !userGroupPermission.allowedServiceTemplates.includes(resourceType)) {
          return { result: AuthorizeResult.DENY };
        }

        return createCatalogConditionalDecision(permission, {
          anyOf: [
            catalogConditions.isEntityKind({
              kinds: userGroupPermission.allowedEntityKinds,
            }),
            isTypeServiceTemplate({
              typeService: userGroupPermission.allowedServiceTemplates,
            }),
          ],
        });
      }

      return this.handleAnonymousUser(permission, resource);
    }

    if (isResourcePermission(permission, "catalog-entity")) {
      return createCatalogConditionalDecision(permission, {
        anyOf: [
          catalogConditions.isEntityOwner({
            //@ts-ignore
            claims: user?.info?.ownershipEntityRefs ?? [],
          }),
        ],
      });
    }

    return { result: AuthorizeResult.DENY };
  }

  private handleAnonymousUser(permission: PolicyQueryPermission, resource?: any): PolicyDecision {
    if (isPermission(permission, catalogEntityReadPermission)) {
      const resourceType = resource?.spec?.type;
      if (resourceType && !permissionData.allowCatalogReadAnyUser.serviceTemplate.includes(resourceType)) {
        return { result: AuthorizeResult.DENY };
      }

      return createCatalogConditionalDecision(permission, {
        anyOf: [
          catalogConditions.isEntityKind({
            kinds: permissionData.allowCatalogReadAnyUser.entityKind,
          }),
          isTypeServiceTemplate({
            typeService: permissionData.allowCatalogReadAnyUser.serviceTemplate,
          }),
        ],
      });
    }

    return { result: AuthorizeResult.DENY };
  }

  private isInGroup(group: string, user?: PolicyQueryUser): boolean {
    if (!user || !user.identity || !user.identity.ownershipEntityRefs) {
      return false;
    }
    return user.identity.ownershipEntityRefs.includes(group);
  }
}

import express, { Request, Response } from "express";
import bodyParser from "body-parser";
import { PermissionPolicy } from "./permissionPolicy";
import { generateToken, validateToken, authenticate } from "./auth";

const app = express();
const port = process.env.PORT || 3000;

app.use(bodyParser.json());

const permissionPolicy = new PermissionPolicy();

app.get("/health", (req: Request, res: Response) => {
  res.json({ status: "OK" });
});
/**
 * Rota para gerar um token JWT.
 */
app.post("/login", (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ error: "Nome de usuário e senha são obrigatórios." });
  }

  if (authenticate(username, password)) {
    const token = generateToken();
    return res.json({ token });
  } else {
    return res.status(401).json({ error: "Credenciais inválidas." });
  }
});

/**
 * Rota para verificar permissões.
 */
app.post("/authorize", (req, res) => {
  const { permission, user, resource, token } = req.body;

  // Valida o token JWT
  if (!token) {
    return res.status(401).json({ error: "Token JWT ausente." });
  }

  const decoded = validateToken(token);
  if (!decoded) {
    return res.status(401).json({ error: "Token JWT inválido ou expirado." });
  }

  permissionPolicy
    .handle(permission, user, resource)
    .then((decision) => {
      res.json(decision);
    })
    .catch((error) => {
      console.error("Erro ao verificar permissões:", error);
      res.status(500).json({ error: "Erro interno do servidor." });
    });
});

const server = app.listen(port, () => {
  console.log(`Permission service running on port ${port}`);
});

export { app, server };

import { createCatalogPermissionRule } from '@backstage/plugin-catalog-backend/alpha';
import { z } from 'zod';
import { Entity } from '@backstage/catalog-model';
import { createConditionFactory } from '@backstage/plugin-permission-node';
import path from 'path';
import fs from 'fs';

export const isTypeServiceTemplateRule = createCatalogPermissionRule({
  name: 'IS_IN_SYSTEM',
  description: 'Verify if a group can use a type service',
  resourceType: 'catalog-entity',
  paramsSchema: z.object({
    typeService: z.array(z.string()).describe('Type of service'),
  }),
  apply: (resource: Entity, { typeService }) => {
    return resource.spec?.type === typeService;
  },
  toQuery: ({ typeService }) => {
    return {
      allOf: [
        { key: 'kind', values: ['Template'] },
        { key: 'spec.type', values: typeService },
      ],
    };
  },
});

export const isTypeServiceTemplate = createConditionFactory(isTypeServiceTemplateRule);

// Carregar configurações do JSON

export interface GroupPermission {
  groups: string[];
  allowedEntityKinds: string[];
  allowedServiceTemplates: string[];
}

export interface PermissionData {
  adminGroups: string[];
  denyCatalogCreate: boolean;
  groupPermissions: GroupPermission[];
  allowCatalogReadAnyUser: {
      entityKind: string[];
      serviceTemplate: string[];
  };
}

const configPath = path.join(__dirname,'./permissionData.json');
export const permissionData = JSON.parse(fs.readFileSync(configPath, 'utf-8')) as PermissionData;
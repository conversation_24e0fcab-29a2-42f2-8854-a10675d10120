import request from 'supertest';
import { app, server } from '../index';
import { generateToken, validateToken } from '../auth';
import { PermissionPolicy } from '../permissionPolicy';
import { AuthorizeResult } from '../types';
import { catalogEntityReadPermission } from '@backstage/plugin-catalog-common/alpha';

process.env['JWT_SECRET'] = 'minha_chave_secreta_super_segura';
process.env['AUTH_USER'] = 'admin';
process.env['AUTH_PASSWORD'] = 'senha_segura';

describe('POST /authorize', () => {
    let validToken: string;
  
    beforeAll(() => {
      // Gera um token válido para ser usado nos testes
      validToken = generateToken();
    });
  
    it('deve retornar ALLOW com um token válido e permissão correta', async () => {
      const response = await request(app)
        .post('/authorize')
        .send({
          permission: {
            name: 'catalog.entity.read',
            resourceType: 'catalog-entity',
          },
          user: {
            identity: {
              ownershipEntityRefs: ['group:default/alm-administratores'],
            },
          },
          resource: {
            kind: 'Component',
            spec: {
              type: 'application-ocp4',
            },
          },
          token: validToken,
        });
  
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ result: 'ALLOW' });
    });
  
    it('deve retornar erro 401 com um token inválido', async () => {
      const response = await request(app)
        .post('/authorize')
        .send({
          permission: {
            name: 'catalog.entity.read',
            resourceType: 'catalog-entity',
          },
          user: {
            identity: {
              ownershipEntityRefs: ['group:default/alm-administratores'],
            },
          },
          resource: {
            kind: 'Component',
            spec: {
              type: 'application-ocp4',
            },
          },
          token: 'token_invalido',
        });
  
      expect(response.status).toBe(401);
      expect(response.body).toEqual({ error: 'Token JWT inválido ou expirado.' });
    });
  
    it('deve retornar erro 401 se o token estiver ausente', async () => {
      const response = await request(app)
        .post('/authorize')
        .send({
          permission: {
            name: 'catalog.entity.read',
            resourceType: 'catalog-entity',
          },
          user: {
            identity: {
              ownershipEntityRefs: ['group:default/alm-administratores'],
            },
          },
          resource: {
            kind: 'Component',
            spec: {
              type: 'application-ocp4',
            },
          },
          // Token ausente
        });
  
      expect(response.status).toBe(401);
      expect(response.body).toEqual({ error: 'Token JWT ausente.' });
    });
  });

  describe('POST /login', () => {
    beforeAll(() => {
      process.env['JWT_SECRET'] = 'minha_chave_secreta_super_segura';
      process.env['AUTH_USER'] = 'admin';
      process.env['AUTH_PASSWORD'] = 'senha_segura';
      process.env['PORT'] = '32401';
    });  
  it('deve retornar um token JWT válido com credenciais corretas', async () => {
    const response = await request(app)
      .post('/login')
      .send({ username: 'admin', password: 'senha_segura' });

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('token');

    // Valida o token gerado
    const decoded = validateToken(response.body.token);
    expect(decoded).toBeTruthy();
    expect(decoded?.user).toBe('admin');
  });

  it('deve retornar erro 401 com credenciais inválidas', async () => {
    const response = await request(app)
      .post('/login')
      .send({ username: 'admin', password: 'senha_errada' });

    expect(response.status).toBe(401);
    expect(response.body).toEqual({ error: 'Credenciais inválidas.' });
  });

  it('deve retornar erro 400 se username ou password estiverem ausentes', async () => {
    const response = await request(app)
      .post('/login')
      .send({ username: 'admin' }); // Senha ausente

    expect(response.status).toBe(400);
    expect(response.body).toEqual({ error: 'Nome de usuário e senha são obrigatórios.' });
  });
});

// Adicionar teste para verificar o novo sistema de permissões com múltiplos grupos
describe('PermissionPolicy with multiple group permissions', () => {
  let permissionPolicy: PermissionPolicy;
  
  beforeEach(() => {
    permissionPolicy = new PermissionPolicy();
  });

  it('should allow users in specific groups with appropriate permissions', async () => {
    // Usuário no primeiro grupo de permissão
    const userInFirstGroup = {
      identity: {
        ownershipEntityRefs: ['group:default/ebe-tid-sistemas-operacao']
      }
    };
    
    // Usuário no segundo grupo de permissão
    const userInSecondGroup = {
      identity: {
        ownershipEntityRefs: ['group:default/ebe-tid-sistemas-comercial']
      }
    };
    
    // Usuário no terceiro grupo de permissão
    const userInThirdGroup = {
      identity: {
        ownershipEntityRefs: ['group:default/ebe-analistas-fornecedores-iom']
      }
    };

    // Recursos para testar
    const allowedResource = {
      kind: "Component",
      spec: {
        type: "application-blank"
      }
    };

    const deniedResource = {
      kind: "Component",
      spec: {
        type: "application-ocp4"
      }
    };

    // Permissão para leitura no catálogo
    const readPermission = catalogEntityReadPermission;

    // Verificar decisão para usuário no primeiro grupo (deve ter acesso)
    const firstGroupAllowedDecision = await permissionPolicy.handle(readPermission, userInFirstGroup, allowedResource);
    expect(firstGroupAllowedDecision.result).toEqual(AuthorizeResult.CONDITIONAL);
    
    // Verificar decisão para usuário no terceiro grupo (deveria negar application-ocp4)
    const thirdGroupDeniedDecision = await permissionPolicy.handle(readPermission, userInThirdGroup, deniedResource);
    expect(thirdGroupDeniedDecision.result).toEqual(AuthorizeResult.DENY);
  });
});

// Fecha o servidor após todos os testes
afterAll((done) => {
  server.close(() => {
    console.log('Servidor fechado.');
    done();
  });
});